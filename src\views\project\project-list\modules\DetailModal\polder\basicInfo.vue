<template>
  <div class="project-info">
    <a-button type="primary" @click="onEditClick" class="project-info-btn" :loading="loading">
      {{ type === 'detail' ? '编辑' : '确定' }}
    </a-button>
    <div v-for="(item, index) in dataSource" :key="index" class="info-panel">
      <div class="title">{{ item.title }}</div>
      <div class="data-box">
        <div v-for="(ele, idx) in item.indicators" :key="idx" class="item">
          <span class="label" :title="ele.label">{{ ele.label }}：</span>
          <span v-if="type === 'detail' || ele.type == 'detail'" class="value" :title="getSpanValue(ele)">
            {{ getSpanValue(ele) }}
          </span>

          <a-input v-else-if="ele.type === 'input'" :disabled="ele.disabled" v-model="ele.value" class="value" />

          <a-input-number v-else-if="ele.type === 'input-number'" v-model="ele.value" class="value" />

          <a-date-picker v-else-if="ele.type === 'date'" v-model="ele.value" class="value" />

          <a-select v-else-if="ele.type === 'select'" v-model="ele.value" class="value">
            <a-select-option v-for="(el, i) in (ele.options && optionsAll[ele.options]) || []" :key="i" :value="el.key">
              {{ el.value }}
            </a-select-option>
          </a-select>

          <a-radio-group v-else-if="ele.type === 'radio'" :options="ele.options" v-model="ele.value"
            class="value flex-value" />
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="jsx">
import moment from 'moment'
import { getPolder, addPolder, updatePolder } from './services.js'
import { getDistrictTree, getOptions } from '@/api/common'
import { projectInfo } from './config'
import TreeGeneral from '@/components/TreeGeneral/index.vue'
export default {
  name: 'BasicInfo',
  props: {},
  components: { TreeGeneral },
  data() {
    return {
      loading: false,
      dataSource: projectInfo,
      type: 'detail',
      attributes: {},

      optionsAll: {
        // 工程等别
        projectWaitOptions: [],
        // 工程规模
        projectScaleOptions: [],
        // 高程系统
        elevationSystemOptions: [],
        // 堤防级别
        dikeWaitOptions: [],
      },
    }
  },
  created() {
    // 工程等别
    getOptions('projectWait').then(res => {
      this.optionsAll.projectWaitOptions = res.data || []
    })

    // 工程规模
    getOptions('projectScale').then(res => {
      this.optionsAll.projectScaleOptions = res.data || []
    })

    // 高程系统
    getOptions('elevationSystem').then(res => {
      this.optionsAll.elevationSystemOptions = res.data || []
    })

    // 堤防级别
    getOptions('dikeWait').then(res => {
      this.optionsAll.dikeWaitOptions = res.data || []
    })

    this.getAttribute()
  },
  computed: {},
  watch: {},
  methods: {
    getAttribute() {
      getPolder({ projectId: this.$attrs.projectId }).then(res => {
        this.attributes = res.data

        this.dataSource = this.dataSource.map(el => ({
          ...el,
          indicators: el.indicators.map(item => ({
            ...item,
            value:
              item.type == 'select'
                ? this.attributes[item.key] === null
                  ? undefined
                  : `${this.attributes[item.key]}`
                : this.attributes[item.key],
          })),
        }))
      })
    },

    onEditClick() {
      if (this.type === 'detail') {
        this.type = 'edit'
      } else {
        this.type = 'detail'

        // 保存
        const attributes = {}
        this.dataSource.forEach(item => {
          item.indicators.forEach(el => {
            if (el.value) {
              if (el.key === 'completionTime') {
                attributes[el.key] = moment(el.value).format('YYYY-MM-DD')
              } else {
                attributes[el.key] = el.value
              }
            }
          })
        })
        const params = {
          projectId: this.$attrs.projectId,
          ...attributes,
          polderId: this.attributes.polderId,
          baseProject: undefined,
        }

        this.loading = true
        if (this.attributes.polderId) {
          updatePolder(params)
            .then(res => {
              this.getAttribute()
              this.$message.success('保存成功', 3)
            })
            .finally(() => (this.loading = false))
        } else {
          addPolder(params)
            .then(res => {
              this.getAttribute()
              this.$message.success('新增成功', 3)
            })
            .finally(() => (this.loading = false))
        }
      }
    },
    getSpanValue(ele) {
      if (ele.type === 'select') {
        return this.optionsAll[ele.options].find(el => el.key == ele.value)?.value
      }
      if (ele.type === 'radio') {
        return ele.options.find(el => el.value == ele.value)?.label
      }
      if (ele.type === 'detail') {
        return this.attributes?.baseProject?.[ele.key]
      }
      return this.attributes[ele.key] || '-'
    },
  },
}
</script>

<style lang="less" scoped>
.project-info {
  width: 100%;
  height: 100%;
  padding: 0 20px;
  overflow: auto;
  position: relative;

  .project-info-btn {
    position: absolute;
    top: 10px;
    right: 50px;
  }

  .info-panel {
    .title {
      font-weight: 700;
      font-size: 16px;
      padding: 10px 16px;
    }

    &:nth-of-type(2),
    &:nth-of-type(3) {
      .title {
        border-top: 1px solid #e5e6eb;
      }
    }

    .data-box {
      display: flex;
      flex-wrap: wrap;
      margin: 0 20px;

      .item {
        width: 33.33%;
        display: flex;
        line-height: 35px;

        .label {
          width: 50%;
          text-align: right;
          color: #aaa;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .value {
          width: 50%;
          text-align: left;
          color: #333;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .flex-value {
          display: flex;
          align-items: center;
        }
      }
    }
  }
}
</style>
