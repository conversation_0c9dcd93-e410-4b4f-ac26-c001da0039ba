<template>
  <ant-modal
    :visible="open"
    :modal-title="modalTitle"
    :loading="modalLoading"
    modalWidth="900"
    @cancel="cancel"
    modalHeight="500"
    :maskClosable="false"
  >
    <div slot="content">
      <div layout="horizontal">
        <a-form-model
          style="margin-top: 24px"
          ref="form"
          :model="form"
          :rules="rules"
          layout="horizontal"
          v-bind="{
            labelCol: { span: 6 },
            wrapperCol: { span: 8 },
          }"
        >
          <a-form-model-item label="时间维度">
            <a-radio-group v-model="timeDimension" @change="changeTimeDimension">
              <a-radio-button v-for="item in radioOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-radio-button>
            </a-radio-group>
          </a-form-model-item>
          <a-form-model-item label="用水计划" prop="msgId">
            <a-select v-model="form.msgId" placeholder="请选择" @change="changeUseWaterPlan">
              <a-select-option v-for="(d, index) in userWaterOptions" :key="index" :value="d.planReportId">
                {{ d.planName }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="配水时间段">
            <a-input v-model="distributionTimePeriod" disabled />
          </a-form-model-item>
          <a-form-model-item label="【永济灌域】配水量" prop="waterVolumeValue">
            <a-input-number v-model="form.waterVolumeValue" placeholder="请输入" allowClear style="width: 100%" />
          </a-form-model-item>
        </a-form-model>
      </div>
      <ResultWaterScheme v-if="showResultWaterScheme" ref="resultWaterSchemeRef" @close="onCloseResult" />
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">生成配水方案</a-button>
    </template>
  </ant-modal>
</template>

<script>
  import { getCenterReportList, addGenerationWater } from '../services'
  import ResultWaterScheme from './ResultWaterScheme.vue'
  import AntModal from '@/components/pt/dialog/AntModal'

  export default {
    name: 'BasicInfo',
    props: ['radioOptions'],
    components: { AntModal, ResultWaterScheme },
    data() {
      return {
        loading: false,
        modalLoading: false,
        open: false,
        modalTitle: '',

        timeDimension: 1,
        distributionTimePeriod: '',
        showResultWaterScheme: false,
        userWaterItem: {},

        form: {
          msgId: undefined,
          waterVolumeValue: undefined,
        },
        userWaterOptions: [],
        rules: {
          msgId: [{ required: true, message: '用水计划不能为空', trigger: 'change' }],
          waterVolumeValue: [{ required: true, message: '【永济灌域】配水量不能为空', trigger: 'blur' }],
        },
      }
    },
    computed: {},
    created() {
      this.getUseWater()
    },
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      onCloseResult() {
        this.showResultWaterScheme = false
        this.open = false
        this.$emit('ok')
        this.$emit('close')
      },
      handleAdd() {
        this.open = true
        this.modalTitle = '新增配水方案'
      },
      getUseWater() {
        getCenterReportList({ planType: this.timeDimension }).then(res => {
          this.userWaterOptions = res?.data
        })
      },
      changeTimeDimension(val) {
        this.getUseWater()
      },
      changeUseWaterPlan(val) {
        this.userWaterItem = this.userWaterOptions.find(el => el.planReportId == val)
        this.distributionTimePeriod =
          this.userWaterOptions.find(el => el.planReportId == val)?.planStartDate +
          '~' +
          this.userWaterOptions.find(el => el.planReportId == val)?.planEndDate
      },
      submitForm: function () {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            addGenerationWater(this.form)
              .then(response => {
                this.showResultWaterScheme = true
                this.$nextTick(() => this.$refs.resultWaterSchemeRef.handleResult(this.userWaterItem, response.data))
              })
              .finally(() => (this.loading = false))
          } else {
            return false
          }
        })
      },
    },
  }
</script>

<style lang="less" scoped></style>
