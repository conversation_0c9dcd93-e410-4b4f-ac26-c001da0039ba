<template>
  <div class="common-table-page">
    <!-- 筛选栏 -->
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="时间维度">
        <a-radio-group v-model="queryParam.planType" @change="handleQuery">
          <a-radio-button v-for="item in radioOptions" :key="item.value" :value="item.value">
            {{ item.label }}
          </a-radio-button>
        </a-radio-group>
      </a-form-item>

      <a-form-item label="计划时间">
        <a-range-picker allow-clear style="width: 100%" v-model="rangeDate" :placeholder="['开始时间', '结束时间']" />
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          tableTitle="配水方案生成"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleAdd()" v-if="loginOrgId == 10020">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="primary" @click="handleExport()" icon="download" :loading="exportLoading">导出</a-button>
          </div>
        </VxeTable>

        <FormWaterScheme
          v-if="showFormWaterScheme"
          ref="formGenerationRef"
          :radioOptions="radioOptions"
          @ok="getList"
          @close="showFormWaterScheme = false"
        />
        <ResultWaterScheme
          v-if="showResultWaterScheme"
          ref="resultWaterSchemeRef"
          :radioOptions="radioOptions"
          @ok="getList"
          @close="showResultWaterScheme = false"
        />
        <IssuedWaterScheme
          v-if="showIssuedWater"
          ref="formIssuedWaterRef"
          @ok="getList"
          @close="showIssuedWater = false"
        />
        <DetailsWaterScheme
          v-if="showDetailsWater"
          ref="formDetailsWaterRef"
          :radioOptions="radioOptions"
          @ok="getList"
          @close="showDetailsWater = false"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getGenerationWater, deleteGenerationWater } from './services'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import FormWaterScheme from './modules/FormWaterScheme.vue'
  import ResultWaterScheme from './modules/ResultWaterScheme.vue'
  import IssuedWaterScheme from './modules/IssuedWaterScheme.vue'
  import DetailsWaterScheme from './modules/DetailsWaterScheme.vue'
  import excelExport from '@/utils/excelExport.js'
  import moment from 'moment'

  export default {
    name: 'HydropowerStation',
    components: {
      VxeTableForm,
      VxeTable,
      FormWaterScheme,
      IssuedWaterScheme,
      DetailsWaterScheme,
      ResultWaterScheme,
    },
    data() {
      return {
        showFormWaterScheme: false,
        showIssuedWater: false,
        showDetailsWater: false,
        showResultWaterScheme: false,
        loginOrgId: JSON.parse(localStorage.getItem('user'))?.loginOrgId,
        rangeDate: [],
        radioOptions: [
          { label: '5日', value: 1 },
          { label: '旬', value: 2 },
          { label: '月', value: 3 },
        ],

        list: [],
        loading: false,
        exportLoading: false,
        total: 0,

        queryParam: {
          msgName: undefined,
          pageNum: 1,
          pageSize: 10,
          planEndDate: undefined,
          planStartDate: undefined,
          planType: 1,
          isSend: undefined,
        },
        columns: [
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '方案名称',
            field: 'msgName',
            minWidth: 150,
          },
          {
            title: '配水时间段',
            field: 'dispatchDate',
            minWidth: 150,
            slots: {
              default: ({ row }) => {
                return row.planStartDate + '~' + row.planEndDate
              },
            },
          },
          {
            title: '创建时间',
            field: 'createdTime',
            minWidth: 90,
          },
          {
            title: '创建人',
            field: 'createdUserName',
            minWidth: 70,
          },
          {
            title: '方案状态',
            field: 'dispatchDate',
            minWidth: 110,
            slots: {
              default: ({ row }) => {
                return row.isSend == 0 ? '待下发' : row.isSend == 1 ? '已下发' : '-'
              },
            },
          },
          {
            title: '操作',
            field: 'operate',
            width: 180,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleCheck(row)}>查看</a>

                    {this.loginOrgId == 10020 && row.isSend == 0 && <a-divider type='vertical' />}
                    {this.loginOrgId == 10020 && row.isSend == 0 && <a onClick={() => this.handleUpdate(row)}>修改</a>}
                    {this.loginOrgId == 10020 && row.isSend == 0 && <a-divider type='vertical' />}
                    {this.loginOrgId == 10020 && row.isSend == 0 && <a onClick={() => this.handleDelete(row)}>删除</a>}

                    {this.loginOrgId == 10020 && row.isSend == 0 && <a-divider type='vertical' />}
                    {this.loginOrgId == 10020 && row.isSend == 0 && <a onClick={() => this.handleIssued(row)}>下发</a>}
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    computed: {},
    created() {},
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },

      /** 查询列表 */
      getList() {
        this.showFormWaterScheme = false
        this.loading = true

        getGenerationWater({
          ...this.queryParam,
          planStartDate: this.rangeDate[0]?.format('YYYY-MM-DD'),
          planEndDate: this.rangeDate[1]?.format('YYYY-MM-DD'),
          isSend: this.loginOrgId == 10020 ? undefined : 1,
        }).then(response => {
          this.list = response?.data?.data
          this.total = response?.data?.total
          this.loading = false
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.rangeDate = []
        this.queryParam = {
          ...this.queryParam,
          planEndDate: undefined,
          pageNum: 1,
          planStartDate: undefined,
        }
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },

      //详情
      handleCheck(record) {
        this.showDetailsWater = true
        this.$nextTick(() => this.$refs.formDetailsWaterRef.handleDetails(record))
      },
      /* 新增 */
      handleAdd() {
        this.showFormWaterScheme = true
        this.$nextTick(() => this.$refs.formGenerationRef.handleAdd())
      },
      /* 修改 */
      handleUpdate(record) {
        this.showResultWaterScheme = true
        this.$nextTick(() => this.$refs.resultWaterSchemeRef.handleUpdate(record))
      },
      /*下发 */
      handleIssued(record) {
        this.showIssuedWater = true
        this.$nextTick(() => this.$refs.formIssuedWaterRef.handleIssued(record))
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this

        this.$confirm({
          title: '确认删除所选中数据?',
          content: '请确认是否删除当前选中的数据',
          onOk() {
            return deleteGenerationWater({ waterSchemeIds: row.waterSchemeId }).then(res => {
              that.$message.success(`成功删除 ${res.data} 条数据`, 3)
              that.getList()
            })
          },
          onCancel() {},
        })
      },
      //导出
      handleExport() {
        this.exportLoading = true
        getGenerationWater({
          ...this.queryParam,
          planStartDate: this.rangeDate[0]?.format('YYYY-MM-DD'),
          planEndDate: this.rangeDate[1]?.format('YYYY-MM-DD'),
        }).then(res => {
          this.exportLoading = false

          const columnsList = [
            {
              title: '序号',
              field: 'seq',
              minWidth: 80,
            },
            {
              title: '方案名称',
              field: 'msgName',
              minWidth: 150,
            },
            {
              title: '配水时间段',
              field: 'timeSlot',
              minWidth: 150,
            },
            {
              title: '创建时间',
              field: 'createdTime',
              minWidth: 90,
            },
            {
              title: '方案状态',
              field: 'isSend',
              minWidth: 60,
            },
            {},
          ]

          const data = (res.data?.data || []).map((row, i) => ({
            ...row,
            seq: i + 1,
            timeSlot: row.planStartDate + '~' + row.planEndDate,
            isSend: row.isSend == 0 ? '待下发' : row.isSend == 1 ? '已下发' : '-',
          }))

          excelExport(columnsList, data, `${this.$route.meta.title}${moment().format('YYYYMMDDHHmmss')}`)
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .table-operations {
    .ant-btn {
      &:last-child {
        margin-right: 0px;
      }
    }
  }
</style>
