# 水情填报日期限制功能实现说明

## 功能需求
水情填报现在点击新增按钮，需要传参查列表的第一条数据的日期，然后在新增的时候，将这个作为默认值。用户只能在新增的时候选择这个日期到今天的日期。

## 实现方案

### 1. 主页面修改 (index.vue)
- 修改 `handleAdd()` 方法，获取列表第一条数据的日期并传递给FormDrawer组件
- 传递参数：`firstRecordDate = this.list[0].fillDate`

### 2. FormDrawer组件修改 (FormDrawer.vue)

#### 2.1 数据结构调整
- 添加 `firstRecordDate` 变量存储列表第一条记录的日期
- 用于限制新增时的日期选择范围

#### 2.2 方法修改

**handleAdd方法**
- 接收 `firstRecordDate` 参数
- 保存到组件实例变量中

**initData方法**
- 接收 `firstRecordDate` 参数
- 如果传入了第一条记录日期，设置默认日期为该日期的下一天
- 否则使用当前日期

**disabledDate方法**
- 保持原有逻辑：禁用今天之后的日期
- 新增逻辑：如果是新增模式且有第一条记录日期，则禁用第一条记录日期及之前的日期
- 可选择范围：第一条记录日期的下一天到今天

**resetData方法**
- 重置时清空 `firstRecordDate`

## 功能效果

### 示例场景
假设列表第一条数据的日期是 `2025-07-02`：

1. **默认日期**：新增时默认选择 `2025-07-03`
2. **可选择范围**：`2025-07-03` 到今天（今天可选）
3. **禁用日期**：`2025-07-02` 及之前的日期被禁用

### 代码逻辑
```javascript
// 主页面传递参数
const firstRecordDate = this.list.length > 0 ? this.list[0].fillDate : null
this.$refs.formDrawerRef.handleAdd(firstRecordDate)

// FormDrawer接收并处理
if (firstRecordDate) {
  this.formData.date = moment(firstRecordDate).add(1, 'day') // 设置为下一天
}

// 日期禁用逻辑
if (!this.isEdit && this.firstRecordDate) {
  const firstDate = moment(this.firstRecordDate)
  return current <= firstDate.endOf('day') // 禁用第一条记录日期及之前
}
```

## 测试验证
1. 确保列表有数据时，新增默认日期为第一条记录的下一天
2. 确保日期选择器正确禁用第一条记录日期及之前的日期
3. 确保编辑模式不受影响
4. 确保列表为空时功能正常
