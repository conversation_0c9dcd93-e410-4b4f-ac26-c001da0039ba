<template>
  <!-- 增加修改 modalWidth="1500"       modalHeight="700" -->
  <ant-modal :visible="open" :modal-title="formTitle" :loading="modalLoading" modalWidth="1400" modalHeight="728"
    @cancel="cancel">
    <div slot="content">
      <a-row class="form-row" :gutter="32">
        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="title">基本信息</div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">年份：</label>
            <span class="common-value-text,input-width">
              <a-date-picker v-model="form.planYear" mode="year" format="YYYY" :disabled="formTitle == '编辑'"
                placeholder="请选择年份" style="width: 30%" :open="yearShow" @openChange="openChange"
                @panelChange="panelChange" />
            </span>
          </div>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="title" style="margin-top: 15px">指标水量</div>
        </a-col>
        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <VxeTable :key="`table-${formTitle}-${form.list?.length || 0}`" style="margin: 0 16px; height: auto"
            :height="450" border size="small" :autoHeight="true" ref="vxeTableRef" :isShowTableHeader="false"
            :columns="columns" :tableData="form.list" :tablePage="false" :rowConfig="{ isHover: false }"
            :showFooter="true" :footerData="footerData"
            :footer-row-style="{ background: '#F8F8F9', fontWeight: 'bold', textAlign: 'center' }"
            :scrollY="{ enabled: true, gt: 0 }" :scrollX="{ enabled: true, gt: 0 }" />
        </a-col>
      </a-row>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>

<script lang="jsx">
import AntModal from '@/components/pt/dialog/AntModal'
import moment from 'moment'
import VxeTable from '@/components/VxeTable/index.vue'
import { decimalFilter } from '@/utils/dealNumber.js'
import { getIrrigationIndicatorWater, addIrrigationIndicatorWater, updateIrrigationIndicatorWater } from '../services'
import * as _ from 'lodash'

export default {
  name: 'FormDrawer',
  props: ['reportUnitOptions'],
  components: { AntModal, VxeTable },
  data() {
    return {
      open: false,
      modalLoading: false,
      formTitle: '',
      form: {
        planYear: moment().format('YYYY'),
      },
      yearShow: false, //年份打开关闭状态，true为打开，false为关闭
      loading: false,
      list: [],
      filteredList: [],
      selectedChannels: [10021, 10022, 10035, 10070], // 南边渠供水所、北边渠供水所、合济渠供水所、永济渠供水所
      columns: [],
    }
  },
  computed: {
    // 合计 全年干口 - 只计算指定的四个供水所
    totalDryAmount() {
      if (!this.form.list || this.form.list.length === 0) return '0.00'
      let sum = this.form.list
        .filter(item => this.selectedChannels.includes(item.depId))
        .reduce((sum, item) => {
          const value = parseFloat(item.dryAmount)
          return sum + (isNaN(value) ? 0 : value)
        }, 0)
      return decimalFilter(sum, 2)
    },
    // 合计 全年直口 - 只计算指定的四个供水所
    totalStraightAmount() {
      if (!this.form.list || this.form.list.length === 0) return '0.00'
      let sum = this.form.list
        .filter(item => this.selectedChannels.includes(item.depId))
        .reduce((sum, item) => {
          const value = parseFloat(item.straightAmount)
          return sum + (isNaN(value) ? 0 : value)
        }, 0)
      return decimalFilter(sum, 2)
    },
    // 合计 春夏灌干口 - 只计算指定的四个供水所
    totalSsDryAmount() {
      if (!this.form.list || this.form.list.length === 0) return '0.00'
      let sum = this.form.list
        .filter(item => this.selectedChannels.includes(item.depId))
        .reduce((sum, item) => {
          const value = parseFloat(item.ssDryAmount)
          return sum + (isNaN(value) ? 0 : value)
        }, 0)
      return decimalFilter(sum, 2)
    },
    // 合计 春夏灌直口 - 只计算指定的四个供水所
    totalSsStraightAmount() {
      if (!this.form.list || this.form.list.length === 0) return '0.00'
      let sum = this.form.list
        .filter(item => this.selectedChannels.includes(item.depId))
        .reduce((sum, item) => {
          const value = parseFloat(item.ssStraightAmount)
          return sum + (isNaN(value) ? 0 : value)
        }, 0)
      return decimalFilter(sum, 2)
    },
    // 合计 秋灌干口 - 只计算指定的四个供水所
    totalAutDryAmount() {
      if (!this.form.list || this.form.list.length === 0) return '0.00'
      let sum = this.form.list
        .filter(item => this.selectedChannels.includes(item.depId))
        .reduce((sum, item) => {
          const value = parseFloat(item.autDryAmount)
          return sum + (isNaN(value) ? 0 : value)
        }, 0)
      return decimalFilter(sum, 2)
    },
    // 合计 秋灌直口 - 只计算指定的四个供水所
    totalAutStraightAmount() {
      if (!this.form.list || this.form.list.length === 0) return '0.00'
      let sum = this.form.list
        .filter(item => this.selectedChannels.includes(item.depId))
        .reduce((sum, item) => {
          const value = parseFloat(item.autStraightAmount)
          return sum + (isNaN(value) ? 0 : value)
        }, 0)
      return decimalFilter(sum, 2)
    },
    // 合计 秋浇干口 - 只计算指定的四个供水所
    totalAutPourDryAmount() {
      if (!this.form.list || this.form.list.length === 0) return '0.00'
      let sum = this.form.list
        .filter(item => this.selectedChannels.includes(item.depId))
        .reduce((sum, item) => {
          const value = parseFloat(item.autPourDryAmount)
          return sum + (isNaN(value) ? 0 : value)
        }, 0)
      return decimalFilter(sum, 2)
    },
    // 合计 秋浇直口 - 只计算指定的四个供水所
    totalAutPourStraightAmount() {
      if (!this.form.list || this.form.list.length === 0) return '0.00'
      let sum = this.form.list
        .filter(item => this.selectedChannels.includes(item.depId))
        .reduce((sum, item) => {
          const value = parseFloat(item.autPourStraightAmount)
          return sum + (isNaN(value) ? 0 : value)
        }, 0)
      return decimalFilter(sum, 2)
    },
    // 表尾数据
    footerData() {
      return [
        {
          depId: '灌域合计',
          dryAmount: this.totalDryAmount,
          straightAmount: this.totalStraightAmount,
          ssDryAmount: this.totalSsDryAmount,
          ssStraightAmount: this.totalSsStraightAmount,
          autDryAmount: this.totalAutDryAmount,
          autStraightAmount: this.totalAutStraightAmount,
          autPourDryAmount: this.totalAutPourDryAmount,
          autPourStraightAmount: this.totalAutPourStraightAmount,
        }
      ]
    },
  },

  mounted() { },
  watch: {
    'form.list': {
      handler() {
        this.filteredList = this.form.list?.filter(item => this.selectedChannels.includes(item.depId))
        // 移除 dealColumns() 调用，避免输入时重新渲染表格
      },
      deep: true,
      // immediate: true,
    },
    filteredList: {
      handler() {
        // 移除 dealColumns() 调用，避免输入时重新渲染表格
      },
      deep: true,
      // immediate: true,
    },
  },
  created() { },
  methods: {
    cancel() {
      this.open = false
    },
    handleAdd() {
      this.open = true
      this.formTitle = '新增'

      // 重置form对象
      this.form = {
        planYear: moment().format('YYYY'),
        list: []
      }

      this.$nextTick(() => {
        this.initData()
        // 初始化表格列配置
        this.dealColumns()
      })
    },
    handleUpdate(row) {
      this.open = true
      this.formTitle = '编辑'
      getIrrigationIndicatorWater({ planYear: row.planYear }).then(res => {
        this.form = {
          ...row,
          planYear: moment(row.planYear, 'YYYY'),
          list: res.data,
        }
        // 初始化表格列配置
        this.$nextTick(() => {
          this.dealColumns()
        })
      })
    },
    initData() {
      // 使用 Vue.set 确保响应式
      this.$set(this.form, 'list', this.reportUnitOptions.map(item => {
        return {
          planYear: null,
          // 干口指标水量（全年）
          dryAmount: 0,
          // 直口指标水量（全年）
          straightAmount: 0,
          // 春夏灌干口指标水量
          ssDryAmount: 0,
          // 春夏灌直口指标水量
          ssStraightAmount: 0,
          // 秋灌干口指标水量
          autDryAmount: 0,
          // 秋灌直口指标水量
          autStraightAmount: 0,
          // 秋浇干口指标水量
          autPourDryAmount: 0,
          // 秋浇直口指标水量
          autPourStraightAmount: 0,
          depId: item.value,
          depName: item.label,
        }
      }))

      this.$nextTick(() => {
        setTimeout(() => {
          this.filteredList = this.form.list?.filter(item => this.selectedChannels.includes(item.depId))
          // 强制更新一次，确保计算属性重新计算
          this.$forceUpdate()
        }, 200)
      })
    },
    dealColumns() {
      this.columns = [
        {
          title: '单位',
          field: 'depId',
          align: 'center',
          width: 120,
          slots: {
            header: () => {
              return (
                <div class='first-col'>
                  <div class='first-col-top'>水量</div>
                  <div class='first-col-bottom'>单位</div>
                </div>
              )
            },
            default: ({ row }) => {
              return this.reportUnitOptions.find(item => item.value == row.depId)?.label || row.depId
            },
          },
        },
        {
          title: '全年指标水量（万m³）',
          children: [
            {
              title: '干口',
              field: 'dryAmount',
              minWidth: 120,
              slots: {
                default: ({ row }) => {
                  return (
                    <div class='table-cell-box'>
                      <a-input-number size='small' v-model={row.dryAmount} min={0} precision={2} />
                    </div>
                  )
                },

              },
            },
            {
              title: '直口',
              field: 'straightAmount',
              minWidth: 120,
              slots: {
                default: ({ row }) => {
                  return (
                    <div class='table-cell-box'>
                      <a-input-number size='small' v-model={row.straightAmount} min={0} precision={2} />
                    </div>
                  )
                },

              },
            },
          ],
        },
        {
          title: '春夏灌指标水量（万m³）',
          children: [
            {
              title: '干口',
              field: 'ssDryAmount',
              minWidth: 120,
              slots: {
                default: ({ row }) => {
                  return (
                    <div class='table-cell-box'>
                      <a-input-number size='small' v-model={row.ssDryAmount} min={0} precision={2} />
                    </div>
                  )
                },

              },
            },
            {
              title: '直口',
              field: 'ssStraightAmount',
              minWidth: 120,
              slots: {
                default: ({ row }) => {
                  return (
                    <div class='table-cell-box'>
                      <a-input-number size='small' v-model={row.ssStraightAmount} min={0} precision={2} />
                    </div>
                  )
                },

              },
            },
          ],
        },
        {
          title: '秋灌指标水量（万m³）',
          children: [
            {
              title: '干口',
              field: 'autDryAmount',
              minWidth: 120,
              slots: {
                default: ({ row }) => {
                  return (
                    <div class='table-cell-box'>
                      <a-input-number size='small' v-model={row.autDryAmount} min={0} precision={2} />
                    </div>
                  )
                },

              },
            },
            {
              title: '直口',
              field: 'autStraightAmount',
              minWidth: 120,
              slots: {
                default: ({ row }) => {
                  return (
                    <div class='table-cell-box'>
                      <a-input-number size='small' v-model={row.autStraightAmount} min={0} precision={2} />
                    </div>
                  )
                },
              },
            },
          ],
        },
        {
          title: '秋浇指标水量（万m³）',
          children: [
            {
              title: '干口',
              field: 'autPourDryAmount',
              minWidth: 120,
              slots: {
                default: ({ row }) => {
                  return (
                    <div class='table-cell-box'>
                      <a-input-number size='small' v-model={row.autPourDryAmount} min={0} precision={2} />
                    </div>
                  )
                },
              },
            },
            {
              title: '直口',
              field: 'autPourStraightAmount',
              minWidth: 120,
              slots: {
                default: ({ row }) => {
                  return (
                    <div class='table-cell-box'>
                      <a-input-number size='small' v-model={row.autPourStraightAmount} min={0} precision={2} />
                    </div>
                  )
                },
              },
            },
          ],
        },
      ]
    },

    openChange(status) {
      if (status) {
        this.yearShow = true
      }
    },
    panelChange(date) {
      this.form.planYear = moment(date).format('YYYY')
      this.yearShow = false
    },
    strictValidate() {
      return this.form.list.every(item => {
        return [
          'dryAmount', 'straightAmount',
          'ssDryAmount', 'ssStraightAmount',
          'autDryAmount', 'autStraightAmount',
          'autPourDryAmount', 'autPourStraightAmount'
        ].every(field => {
          return item[field] !== null && item[field] !== undefined && item[field] !== ''
        })
      })
    },
    submitForm() {
      if (!this.strictValidate()) {
        this.$message.warn('数据不能为空，请填写完整数据！')
        return
      }
      this.loading = true
      this.form.list.forEach(item => {
        item.planYear = parseInt(moment(this.form.planYear).format('YYYY'))
      })
      if (this.formTitle === '新增') {
        addIrrigationIndicatorWater(this.form.list)
          .then(res => {
            if (res.code === 200) {
              this.$message.success('新增成功！')
              this.$emit('close')
              this.$emit('ok')
              this.open = false
              this.loading = false
            }
          })
          .catch(err => {
            this.loading = false
          })
      } else {
        updateIrrigationIndicatorWater(this.form.list)
          .then(res => {
            if (res.code === 200) {
              this.$message.success('编辑成功！')
              this.$emit('close')
              this.$emit('ok')
              this.open = false
              this.loading = false
            }
          })
          .catch(err => {
            this.loading = false
          })
      }
    },
  },
}
</script>
<style lang="less" scoped>
@import url('~@/global.less');

.title {
  color: #333;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: bold;
}

.details-img {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  img {
    width: 100%;
    height: 100%;
  }
}

.input-width {
  // width: 100%;
  margin-right: 0px;
}

.item {
  // display: flex;
  // align-items: center;
  // margin-bottom: 10px;
  margin: 10px 0;
}

::v-deep(.header-bar) {
  .title {
    text-align: center;
    width: 100%;
    position: absolute;
  }
}

::v-deep(.vxe-table--header) {
  .first-col {
    position: relative;
    height: 40px;

    // background:red;
    &:before {
      content: '';
      background-color: #e8eaec;
      width: 300px;
      height: 1px;
      position: absolute;
      top: 54px;
      left: -20px;
      transform: rotate(22deg);
    }

    .first-col-top {
      position: absolute;
      right: 4px;
      top: -10px;
      right: 8px;
      top: -2px;
    }

    .first-col-bottom {
      position: absolute;
      left: 4px;
      bottom: -20px;
      left: 12px;
      bottom: 0px;
    }
  }
}

::v-deep(.vxe-table--footer) {

  .vxe-body--column,
  .vxe-footer--column {
    text-align: center !important;
  }

  .vxe-cell {
    text-align: center !important;
    justify-content: center !important;
  }
}
</style>
