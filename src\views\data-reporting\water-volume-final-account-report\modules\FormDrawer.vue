<template>
  <!-- 增加修改 -->
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="1500"
    @cancel="cancel"
    modalHeight="700"
  >
    <div slot="content">
      <a-row class="form-row" :gutter="32">
        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="title">基本信息</div>
        </a-col>
        <div class="form-border info">
          <a-col :lg="12" :md="12" :sm="24">
            <div class="item">
              <label class="common-label-text">年份：</label>
              <span class="common-value-text">
                <!-- v-decorator="[
                    'year',
                    {
                      rules: [{ required: true, message: '请选择年份' }],
                    },
                  ]" -->
                <a-date-picker
                  v-model="form.accountYear"
                  mode="year"
                  format="YYYY"
                  placeholder="请选择年份"
                  style="width: 30%"
                  :open="yearShow"
                  :disabled="formTitle == '编辑'"
                  @openChange="openChange"
                  @panelChange="panelChange"
                />
              </span>
            </div>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <div class="item">
              <label class="common-label-text">填报单位：</label>
              <span class="common-value-text">
                <!-- {{ form?.reportUnit }} -->
                <a-select
                  v-model="form.depId"
                  placeholder="请选择填报单位"
                  style="width: 200px"
                  :disabled="formTitle == '编辑'"
                  :options="reportUnitOptions"
                />
              </span>
            </div>
          </a-col>
        </div>

        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="title">水量决算</div>
        </a-col>
        <!-- style="width: 90%; height: 400px; margin-top: 60px" -->
        <div class="form-border detail">
          <div class="form-border-item">实用水量</div>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item detail-item">
              <label class="common-label-text">春夏灌：</label>
              <span class="common-value-text">
                <!-- {{ form?.practicalWaterVolume?.SpringSummer }} waterAmounts['1-0-1'] -->

                <a-input-number
                  v-model="form.data.find(item => item.type === '1-0').SpringSummer"
                  :precision="2"
                  :min="0"
                  allowClear
                  placeholder="请输入"
                />
                万m³
              </span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item detail-item">
              <label class="common-label-text">秋灌：</label>
              <span class="common-value-text">
                <!-- {{ form?.practicalWaterVolume?.AutumnIrrigation }} waterAmounts['1-0-2']-->

                <a-input-number
                  v-model="form.data.find(item => item.type === '1-0').AutumnIrrigation"
                  :precision="2"
                  :min="0"
                  allowClear
                  placeholder="请输入"
                />
                万m³
              </span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item detail-item">
              <label class="common-label-text">秋浇：</label>
              <span class="common-value-text">
                <!-- {{ form.practicalWaterVolume.AutumnIrrigation2 }} waterAmounts['1-0-3']-->
                <a-input-number
                  v-model="form.data.find(item => item.type === '1-0').AutumnIrrigation2"
                  :precision="2"
                  :min="0"
                  allowClear
                  placeholder="请输入"
                />
                万m³
              </span>
            </div>
          </a-col>
          <!-- item2 -->
          <div class="form-border-item">指标内直口实用水量</div>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item detail-item">
              <label class="common-label-text">春夏灌：</label>
              <span class="common-value-text">
                <!-- {{ form.withinQuotaDirectOutlet.SpringSummer }} waterAmounts['2-1-1']-->
                <a-input-number
                  v-model="form.data.find(item => item.type === '2-1').SpringSummer"
                  :precision="2"
                  :min="0"
                  allowClear
                  placeholder="请输入"
                />
                万m³
              </span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item detail-item">
              <label class="common-label-text">秋灌：</label>
              <span class="common-value-text">
                <!-- {{ form.withinQuotaDirectOutlet.AutumnIrrigation }} waterAmounts['2-1-2']-->
                <a-input-number
                  v-model="form.data.find(item => item.type === '2-1').AutumnIrrigation"
                  :precision="2"
                  :min="0"
                  allowClear
                  placeholder="请输入"
                />
                万m³
              </span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item detail-item">
              <label class="common-label-text">秋浇：</label>
              <span class="common-value-text">
                <!-- {{ form.withinQuotaDirectOutlet.AutumnIrrigation2 }} waterAmounts['2-1-3']-->
                <a-input-number
                  v-model="form.data.find(item => item.type === '2-1').AutumnIrrigation2"
                  :precision="2"
                  :min="0"
                  allowClear
                  placeholder="请输入"
                />
                万m³
              </span>
            </div>
          </a-col>
          <!-- item3 指标内折算到斗口实用水量-->
          <div class="form-border-item">指标内折算到斗口实用水量</div>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item detail-item">
              <label class="common-label-text">春夏灌：</label>
              <span class="common-value-text">
                <!-- {{ form.withinQuotaTurnout.SpringSummer }} waterAmounts['3-1-1']-->
                <a-input-number
                  v-model="form.data.find(item => item.type === '3-1').SpringSummer"
                  :precision="2"
                  :min="0"
                  allowClear
                  placeholder="请输入"
                />
                万m³
              </span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item detail-item">
              <label class="common-label-text">秋灌：</label>
              <span class="common-value-text">
                <!-- {{ form.withinQuotaTurnout.AutumnIrrigation }} waterAmounts['3-1-2']-->
                <a-input-number
                  v-model="form.data.find(item => item.type === '3-1').AutumnIrrigation"
                  :precision="2"
                  :min="0"
                  allowClear
                  placeholder="请输入"
                />
                万m³
              </span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item detail-item">
              <label class="common-label-text">秋浇：</label>
              <span class="common-value-text">
                <!-- {{ form?.withinQuotaTurnout?.AutumnIrrigation2 }} waterAmounts['3-1-3']-->
                <a-input-number
                  v-model="form.data.find(item => item.type === '3-1').AutumnIrrigation2"
                  :precision="2"
                  :min="0"
                  allowClear
                  placeholder="请输入"
                />
                万m³
              </span>
            </div>
          </a-col>
          <!-- item4 -->
          <div class="form-border-item">指标外直口实用水量</div>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item detail-item">
              <label class="common-label-text">春夏灌：</label>
              <span class="common-value-text">
                <!-- {{ form.extraQuotaDirectOutlet.SpringSummer }} waterAmounts['2-2-1']-->
                <a-input-number
                  v-model="form.data.find(item => item.type === '2-2').SpringSummer"
                  :precision="2"
                  :min="0"
                  allowClear
                  placeholder="请输入"
                />
                万m³
              </span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item detail-item">
              <label class="common-label-text">秋灌：</label>
              <span class="common-value-text">
                <!-- {{ form.extraQuotaDirectOutlet.AutumnIrrigation }} waterAmounts['2-2-2']-->
                <a-input-number
                  v-model="form.data.find(item => item.type === '2-2').AutumnIrrigation"
                  :precision="2"
                  :min="0"
                  allowClear
                  placeholder="请输入"
                />
                万m³
              </span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item detail-item">
              <label class="common-label-text">秋浇：</label>
              <span class="common-value-text">
                <!-- {{ form.extraQuotaDirectOutlet.AutumnIrrigation2 }} waterAmounts['2-2-3']-->
                <a-input-number
                  v-model="form.data.find(item => item.type === '2-2').AutumnIrrigation2"
                  :precision="2"
                  :min="0"
                  allowClear
                  placeholder="请输入"
                />
                万m³
              </span>
            </div>
          </a-col>
          <!-- item5 指标内折算到斗口实用水量-->
          <div class="form-border-item">指标外折算到斗口实用水量</div>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item detail-item">
              <label class="common-label-text">春夏灌：</label>
              <span class="common-value-text">
                <!-- {{ form?.extraQuotaTurnout?.SpringSummer }} waterAmounts['3-2-1']-->
                <a-input-number
                  v-model="form.data.find(item => item.type === '3-2').SpringSummer"
                  :precision="2"
                  :min="0"
                  allowClear
                  placeholder="请输入"
                />
                万m³
              </span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item detail-item">
              <label class="common-label-text">秋灌：</label>
              <span class="common-value-text">
                <!-- {{ form?.extraQuotaTurnout?.AutumnIrrigation }} waterAmounts['3-2-2']-->
                <a-input-number
                  v-model="form.data.find(item => item.type === '3-2').AutumnIrrigation"
                  :precision="2"
                  :min="0"
                  allowClear
                  placeholder="请输入"
                />
                万m³
              </span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item detail-item">
              <label class="common-label-text">秋浇：</label>
              <span class="common-value-text">
                <!-- {{ form?.extraQuotaTurnout?.AutumnIrrigation2 }} waterAmounts['3-2-3']-->
                <a-input-number
                  v-model="form.data.find(item => item.type === '3-2').AutumnIrrigation2"
                  :precision="2"
                  :min="0"
                  allowClear
                  placeholder="请输入"
                />
                万m³
              </span>
            </div>
          </a-col>
        </div>
      </a-row>
      <!-- <div class="details-img">
        <img src="@/assets/images/data-reporting/water-volume-final-account-report.png" alt="" />
      </div> -->
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>

<script lang="jsx">
  import AntModal from '@/components/pt/dialog/AntModal'
  import moment from 'moment'
  import { getIrrigationWaterAccount, addIrrigationWaterAccount, updateIrrigationWaterAccount } from '../services'

  export default {
    name: 'FormDrawer',
    props: ['reportUnitOptions'],
    components: { AntModal },
    data() {
      return {
        open: false,
        modalLoading: false,
        formTitle: '',

        form: {
          accountYear: moment().format('YYYY'),
          depId: null,
          data: [
            {
              //实用水量
              type: '1-0',
              SpringSummer: null, //春夏灌
              AutumnIrrigation: null, //秋灌
              AutumnIrrigation2: null, //秋浇
              depId: null,
              accountYear: null,
            },
            {
              //指标内直口实用水量
              type: '2-1',
              SpringSummer: null, //春夏灌
              AutumnIrrigation: null, //秋灌
              AutumnIrrigation2: null, //秋浇
              depId: null,
              accountYear: null,
            },
            {
              //指标内折算到斗口实用水量
              type: '3-1',
              SpringSummer: null, //春夏灌
              AutumnIrrigation: null, //秋灌
              AutumnIrrigation2: null, //秋浇
              depId: null,
              accountYear: null,
            },
            {
              //指标外直口实用水量
              type: '2-2',
              SpringSummer: null, //春夏灌
              AutumnIrrigation: null, //秋灌
              AutumnIrrigation2: null, //秋浇
              depId: null,
              accountYear: null,
            },
            {
              //指标外折算到斗口实用水量
              type: '3-2',
              SpringSummer: null, //春夏灌
              AutumnIrrigation: null, //秋灌
              AutumnIrrigation2: null, //秋浇
              depId: null,
              accountYear: null,
            },
          ],
        },
        yearShow: false, //年份打开关闭状态，true为打开，false为关闭
        loading: false,
        waterAmounts: {},
        dataList: [], // 初始为空数组，等待接口返回数据
      }
    },
    computed: {},
    waterAmounts: {
      // handler(newVal) {
      //   this.waterAmountsComputed = newVal
      // },
      // deep: true,
    },
    methods: {
      reset() {
        this.form = {
          accountYear: moment().format('YYYY'),
          depId: null,
          data: [
            {
              //实用水量
              type: '1-0',
              SpringSummer: null, //春夏灌
              AutumnIrrigation: null, //秋灌
              AutumnIrrigation2: null, //秋浇
              depId: null,
              accountYear: null,
            },
            {
              //指标内直口实用水量
              type: '2-1',
              SpringSummer: null, //春夏灌
              AutumnIrrigation: null, //秋灌
              AutumnIrrigation2: null, //秋浇
              depId: null,
              accountYear: null,
            },
            {
              //指标内折算到斗口实用水量
              type: '3-1',
              SpringSummer: null, //春夏灌
              AutumnIrrigation: null, //秋灌
              AutumnIrrigation2: null, //秋浇
              depId: null,
              accountYear: null,
            },
            {
              //指标外直口实用水量
              type: '2-2',
              SpringSummer: null, //春夏灌
              AutumnIrrigation: null, //秋灌
              AutumnIrrigation2: null, //秋浇
              depId: null,
              accountYear: null,
            },
            {
              //指标外折算到斗口实用水量
              type: '3-2',
              SpringSummer: null, //春夏灌
              AutumnIrrigation: null, //秋灌
              AutumnIrrigation2: null, //秋浇
              depId: null,
              accountYear: null,
            },
          ],
        }
      },
      cancel() {
        this.open = false
      },
      //将原始数据转换为表单数据
      getConvertList(list) {
        const groupedMap = new Map()

        list.forEach(item => {
          const key = `${item.waterType}-${item.indicatorType}`

          if (!groupedMap.has(key)) {
            groupedMap.set(key, {
              type: key,
              SpringSummer: 0,
              AutumnIrrigation: 0,
              AutumnIrrigation2: 0,
              depId: item.depId,
              accountYear: item.accountYear,
            })
          }

          const group = groupedMap.get(key)
          switch (item.irrigationType) {
            case 1:
              group.SpringSummer = item.waterAmount
              break
            case 2:
              group.AutumnIrrigation = item.waterAmount
              break
            case 3:
              group.AutumnIrrigation2 = item.waterAmount
              break
          }
        })

        return Array.from(groupedMap.values())
      },
      //将表单数据转换为原始数据
      getNewList(newList) {
        const result = []

        newList.forEach(item => {
          const [waterType, indicatorType] = item.type.split('-').map(Number)

          result.push({
            accountYear: this.form.accountYear,
            depId: this.form.depId,
            waterType,
            indicatorType,
            irrigationType: 1,
            waterAmount: item.SpringSummer,
          })

          result.push({
            accountYear: this.form.accountYear,
            depId: this.form.depId,
            waterType,
            indicatorType,
            irrigationType: 2,
            waterAmount: item.AutumnIrrigation,
          })

          result.push({
            accountYear: this.form.accountYear,
            depId: this.form.depId,
            waterType,
            indicatorType,
            irrigationType: 3,
            waterAmount: item.AutumnIrrigation2,
          })
        })

        return result
      },
      handleAdd() {
        this.open = true
        this.formTitle = '新增'
        this.reset()
      },
      handleUpdate(row) {
        console.log('row 查看', row, row.accountYear)

        this.open = true
        this.formTitle = '编辑'
        getIrrigationWaterAccount({ accountYear: row.accountYear, depId: row.depId }).then(res => {
          this.dataList = res.data
          this.form = {
            ...row,
            accountYear: moment(row.accountYear, 'YYYY'),
            data: this.getConvertList(res.data),
          }
          console.log('详情 666', this.form)
        })
      },
      /** waterType水量类型 1 实用水量 2 直口 3 斗口  indicatorType指标类型 1 指标内 2指标外 0 全指标  irrigationType灌类型 1 春夏灌 2秋灌 3 秋浇 */

      openChange(status) {
        if (status) {
          this.yearShow = true
        }
      },
      panelChange(date) {
        console.log('get year', date)
        this.form.accountYear = moment(date).format('YYYY')
        this.yearShow = false
      },
      strictValidate() {
        return this.form.data.every(item => {
          return ['SpringSummer', 'AutumnIrrigation', 'AutumnIrrigation2'].every(field => {
            return item[field] !== null && item[field] !== undefined
          })
        })
      },
      submitForm() {
        if (!this.strictValidate()) {
          this.$message.warn('数据不能为空，请填写完整数据！')
          return
        }
        this.loading = true
        this.form.accountYear = moment(this.form.accountYear).format('YYYY')
        let list = this.getNewList(this.form.data)
        console.log('save list', list)
        if (this.formTitle === '新增') {
          addIrrigationWaterAccount(list)
            .then(res => {
              if (res.code === 200) {
                this.$message.success('新增成功！')
                this.$emit('close')
                this.$emit('ok')
                this.open = false
                this.loading = false
              }
            })
            .catch(err => {
              this.loading = false
            })
        } else {
          updateIrrigationWaterAccount(list)
            .then(res => {
              if (res.code === 200) {
                this.$message.success('编辑成功！')
                this.$emit('close')
                this.$emit('ok')
                this.open = false
                this.loading = false
              }
            })
            .catch(err => {
              this.loading = false
            })
        }
      },
    },
  }
</script>
<style lang="less" scoped>
  @import url('~@/global.less');
  .title {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 12px;
  }
  .form-border {
    border: 1px solid #e8e8e8;
    padding: 10px 20px;
    margin: 26px 14px;
  }
  .info {
    width: 70%;
    margin: 38px 14px 10px 14px;
    padding: 10px 20px;
    height: 54px;
  }
  .detail {
    width: 90%;
    height: 400px;
    margin-top: 46px;
  }
  .detail-item {
    margin-bottom: 10px;
  }
  .form-border-item {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 10px;
  }
  .details-img {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    img {
      width: 100%;
      height: 100%;
    }
  }
</style>
