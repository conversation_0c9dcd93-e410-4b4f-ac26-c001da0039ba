<template>
  <!-- 增加修改 -->
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="1200"
    @cancel="cancel"
    modalHeight="900"
  >
    <div slot="content">
      <h3>注：单位：Q日，1Q日=8.64万m³</h3>
      <h4>配水参数获取方式：用水计划</h4>
      <VxeTable
        :key="31"
        style="margin: 0 16px; height: auto"
        :height="useWaterData.length > 2 ? 521 : 330"
        border
        size="small"
        :autoHeight="true"
        ref="vxeTableRef"
        :isShowTableHeader="false"
        :columns="useWaterColumns"
        :tableData="useWaterData"
        :tablePage="false"
        :rowConfig="{ isHover: false }"
        :mergeCells="[
          { row: 0, col: 0, rowspan: 9, colspan: 1 },
          { row: 0, col: 1, rowspan: 1, colspan: 2 },
          { row: 1, col: 1, rowspan: 1, colspan: 2 },
          { row: 2, col: 1, rowspan: 1, colspan: 2 },
          { row: 3, col: 1, rowspan: 6, colspan: 1 },
        ]"
        show-footer
        :footerMethod="footerMethod"
        :merge-footer-items="[
          { row: 1, col: 0, rowspan: 1, colspan: 3 },
          { row: 2, col: 0, rowspan: 1, colspan: 3 },
          { row: 2, col: 3, rowspan: 1, colspan: 999 },
        ]"
      />

      <h4 style="margin-top: 10px">配水参数获取方式：需水预测模型</h4>
      <VxeTable
        :key="41"
        style="margin: 0 16px; height: auto"
        :height="440"
        border
        size="small"
        :autoHeight="true"
        ref="vxeTableRef"
        :isShowTableHeader="false"
        :columns="schemeColumns"
        :tableData="schemeData"
        :tablePage="false"
        :rowConfig="{ isHover: false }"
        :mergeCells="[
          { row: 0, col: 0, rowspan: 9, colspan: 1 },
          { row: 0, col: 1, rowspan: 1, colspan: 2 },
          { row: 1, col: 1, rowspan: 1, colspan: 2 },
          { row: 2, col: 1, rowspan: 1, colspan: 2 },
          { row: 3, col: 1, rowspan: 6, colspan: 1 },
        ]"
        show-footer
        :footerMethod="modelFooterMethod"
        :merge-footer-items="[
          { row: 1, col: 0, rowspan: 1, colspan: 3 },
          { row: 2, col: 0, rowspan: 1, colspan: 3 },
          { row: 2, col: 3, rowspan: 1, colspan: 999 },
        ]"
      />
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">提交</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getGenerationWaterDetails, editGenerationWater, deleteGenerationWater } from '../services.js'
  import AntModal from '@/components/pt/dialog/AntModal'
  import VxeTable from '@/components/VxeTable/index.vue'
  import { getOptions } from '@/api/common.js'

  import { getPlanReportDetails } from '../services.js'

  import moment from 'moment'
  import { dealNumber, getFixedNum } from '@/utils/dealNumber.js'
  import * as _ from 'lodash'

  export default {
    name: 'DetailsWaterScheme',
    components: { AntModal, VxeTable },
    data() {
      return {
        loading: false,
        modalLoading: false,
        open: false,
        rowInfo: {},

        formTitle: '',
        form: {},
        open: false,
        deptSortList: [],

        useWaterData: [],
        schemeData: [],
        useWaterColumns: [],
        schemeColumns: [],

        columns: [],
        tableData: [],
        modelColumns: [],
        summaries: [],

        waterSchemeId: undefined,
        remarks: '',
      }
    },
    filters: {},
    created() {},
    computed: {},
    watch: {},
    methods: {
      handleClose() {
        this.open = false
      },
      // 取消按钮
      cancel() {
        this.open = false
        if (this.formTitle == '新增配水方案') {
          deleteGenerationWater({ waterSchemeIds: this.waterSchemeId }).then(res => {
            this.$emit('ok')
            this.$emit('close')
          })
        }
      },
      /** 详情按钮操作 */
      handleResult(row, waterSchemeId) {
        this.formTitle = '新增配水方案'
        this.rowInfo = row
        this.open = true
        this.modalLoading = true
        this.getDept()
        this.getUserWater()
        this.getWaterSchemeDetails(waterSchemeId)
        this.waterSchemeId = waterSchemeId
      },
      handleUpdate(row) {
        this.formTitle = '修改配水方案'
        this.rowInfo = row
        this.open = true
        this.modalLoading = true
        this.getDept()
        this.getUserWater()
        this.getWaterSchemeDetails(row.waterSchemeId)
        this.waterSchemeId = row.waterSchemeId
      },
      getDept() {
        getOptions('planWaterDept').then(res => {
          this.deptSortList = res.data
        })
      },
      /** 用水计划表格 */
      getUserWater() {
        Promise.allSettled([
          getPlanReportDetails({ planReportId: this.rowInfo.planReportId || this.rowInfo.msgId }),
          //永济渠总局
          // getSummariesById({ msgId: this.rowInfo.planReportId }),
        ]).then(results => {
          const planMsg = this.setSortDept(results[0]?.value?.data?.reportDetailsVOs) || []
          this.remarks = results[0]?.value?.data?.remarks || ''
          this.summaries = results[0]?.value?.data?.summaries || []

          const useList = planMsg.map(item => {
            const recordObj = {}
            item.records.forEach(item => {
              recordObj[item.planDate] = {
                planDate: item.planDate,
                planStartFlow: item?.planStartFlow,
                planEndFlow: item?.planEndFlow,
              }
            })
            return {
              ...item,
              recordObj,
            }
          })

          this.dealUseWaterColumns(useList)

          // this.$nextTick(() => {
          //   setTimeout(() => {
          //     this.$nextTick(() => {
          //       document.querySelectorAll('.use-water-span').forEach(item => {
          //         item.setAttribute('colspan', '3')
          //       })
          //     })
          //   }, 200)
          // })

          this.modalLoading = false
        })
      },
      getWaterSchemeDetails(id) {
        getGenerationWaterDetails({ waterSchemeId: id }).then(res => {
          let schemeArr = this.setSortDept(res.data) || []
          this.schemeData = schemeArr.map(item => {
            const recordObj = {}
            item.records.forEach(item => {
              recordObj[item.planDate] = {
                planDate: item.planDate,
                planStartFlow: item?.planStartFlow,
                planEndFlow: item?.planEndFlow,
              }
            })
            return {
              ...item,
              recordObj,
            }
          })

          this.dealSchemeColumns()

          // this.$nextTick(() => {
          //   setTimeout(() => {
          //     this.$nextTick(() => {
          //       document.querySelectorAll('.scheme-span').forEach(item => {
          //         item.setAttribute('colspan', '3')
          //       })
          //     })
          //   }, 200)
          // })

          this.modalLoading = false
        })
      },
      sumNum1(list, date, field) {
        let count = 0
        list.forEach((item, idx) => {
          if (idx > 2) {
            count += Number(item.recordObj[date][field])
          }
        })
        return getFixedNum(count, 1)
      },
      sumNum2(list, date, field) {
        let count = 0
        list.forEach((item, idx) => {
          count += Number(item.recordObj[date][field])
        })
        return getFixedNum(count, 1)
      },
      //
      setSortDept(arr) {
        const keyToValue = {}
        this.deptSortList.forEach(item => {
          keyToValue[item.key] = item.value
        })
        if (!arr || arr.length === 0) {
          return []
        }
        const keyToIndex = {}
        this.deptSortList.forEach((item, index) => {
          keyToIndex[item.key] = index
        })
        arr.forEach(item => {
          if (keyToValue[item.deptId]) {
            item.deptName = keyToValue[item.deptId]
          }
        })
        arr.sort((a, b) => {
          const indexA = keyToIndex[a.deptId] ?? Infinity
          const indexB = keyToIndex[b.deptId] ?? Infinity
          return indexA - indexB
        })
        return arr
      },
      footerMethod({ columns, data }) {
        const footerData = [
          columns.map((column, _columnIndex) => {
            if (_columnIndex === 2) {
              return '永济渠合计'
            }
            if (_columnIndex > 2 && _columnIndex < columns.length - 1) {
              return `${this.sumNum1(data, column.property, 'planStartFlow')} ~ ${this.sumNum1(data, column.property, 'planEndFlow')}`
            }
            return null
          }),
          columns.map((column, _columnIndex) => {
            if (_columnIndex === 0) {
              return '永济渠报总局'
            }
            if (_columnIndex > 2 && _columnIndex < columns.length - 1) {
              return `${this.sumNum2(data, column.property, 'planStartFlow')} ~ ${this.sumNum2(data, column.property, 'planEndFlow')}`
            }
            return null
          }),
          columns.map((column, _columnIndex) => {
            if (_columnIndex === 0) {
              return '备注'
            }
            if (_columnIndex > 2 && _columnIndex < columns.length - 1) {
              return `${this.sumNum2(data, column.property, 'planStartFlow')} ~ ${this.sumNum2(data, column.property, 'planEndFlow')}`
            }
            return null
          }),
        ]
        return footerData
      },
      modelFooterMethod({ columns, data }) {
        if (this.schemeData?.length == 0) {
          return []
        }
        const footerData = [
          columns.map((column, _columnIndex) => {
            if (_columnIndex === 2) {
              return '永济渠合计'
            }
            if (_columnIndex > 2 && _columnIndex < columns.length - 1) {
              return `${this.sumNum1(data, column.property, 'planStartFlow')} ~ ${this.sumNum1(data, column.property, 'planEndFlow')}`
            }
            return null
          }),
        ]
        return footerData
      },
      dealUseWaterColumns(tableData) {
        const dateRange = [moment(this.rowInfo.planStartDate), moment(this.rowInfo.planEndDate)]
        this.useWaterData = tableData
        this.useWaterColumns = [
          {
            title: '所名',
            field: 'deptName',
            align: 'center',
            width: 40,
            fixed: 'left',
            headerClassName: 'use-water-span',
            slots: {
              default: () => (
                <div style='writing-mode: vertical-rl;width: 100%; display: flex; align-items: center; letter-spacing: 6px'>
                  永济灌域
                </div>
              ),
            },
          },
          {
            title: '',
            field: 'deptName',
            align: 'center',
            minWidth: 90,
            fixed: 'left',
            headerClassName: 'hidden-cell',
            showOverflow: true,
            slots: {
              default: ({ row, rowIndex }) => {
                if (rowIndex < 3) {
                  return <div class='table-cell-box'>{row.deptName}</div>
                }
                return '永济渠'
              },
            },
          },
          {
            title: '',
            field: 'deptName',
            align: 'center',
            minWidth: 90,
            fixed: 'left',
            headerClassName: 'hidden-cell',
            showOverflow: true,
          },
          // 生成日期列
          ...[...Array(dateRange[1].diff(dateRange[0], 'day') + 1)].map((item, index) => {
            const date = moment(dateRange[0]).add(index, 'day').format('YYYY-MM-DD')

            return {
              title: moment(dateRange[0]).add(index, 'day').format('MM月DD日'),
              align: 'center',
              minWidth: 140,
              field: moment(dateRange[0]).add(index, 'day').format('YYYY-MM-DD'),
              slots: {
                default: ({ row, rowIndex }) => {
                  return (
                    <div class='table-cell-box'>
                      <a-input-number
                        size='small'
                        v-model={this.useWaterData[rowIndex].recordObj[date].planStartFlow}
                        min={0}
                        precision={1}
                        onChange={val => {
                          this.$nextTick(() => {
                            const obj = this.useWaterData[rowIndex].recordObj[date]
                            if (obj.planStartFlow > obj.planEndFlow) {
                              obj.planStartFlow = obj.planEndFlow
                            }
                          })
                        }}
                      />
                      &nbsp;~&nbsp;
                      <a-input-number
                        size='small'
                        v-model={this.useWaterData[rowIndex].recordObj[date].planEndFlow}
                        min={0}
                        precision={1}
                        onChange={val => {
                          this.$nextTick(() => {
                            const obj = this.useWaterData[rowIndex].recordObj[date]
                            if (obj.planEndFlow < obj.planStartFlow) {
                              obj.planEndFlow = obj.planStartFlow
                            }
                          })
                        }}
                      />
                    </div>
                  )
                },
                footer: rowInfo => {
                  // 永济渠合计
                  if (rowInfo.rowIndex === 0) {
                    // return rowInfo.row[rowInfo.itemIndex]
                    let yjStart = this.sumNum1(this.useWaterData, rowInfo.column.property, 'planStartFlow')
                    let yjEnd = this.sumNum1(this.useWaterData, rowInfo.column.property, 'planEndFlow')
                    return (
                      <div class='table-cell-box'>
                        <a-input-number size='small' v-model={yjStart} min={0} precision={1} disabled={true} />
                        &nbsp;~&nbsp;
                        <a-input-number size='small' v-model={yjEnd} min={0} precision={1} disabled={true} />
                      </div>
                    )
                  }
                  // 永济渠报总局
                  if (rowInfo.rowIndex === 1) {
                    if (rowInfo.itemIndex < 2 || rowInfo.itemIndex === rowInfo.items.length - 1)
                      return rowInfo.row[rowInfo.itemIndex]

                    return (
                      <div class='table-cell-box'>
                        <a-input-number
                          size='small'
                          v-model={this.summaries[rowInfo.itemIndex - 3].planStartFlow}
                          min={0}
                          precision={1}
                          onChange={val => {
                            this.$nextTick(() => {
                              const obj = this.summaries[rowInfo.itemIndex - 3]
                              if (obj.planStartFlow > obj.planEndFlow) {
                                obj.planStartFlow = obj.planEndFlow
                              }
                            })
                          }}
                        />
                        &nbsp;~&nbsp;
                        <a-input-number
                          size='small'
                          v-model={this.summaries[rowInfo.itemIndex - 3].planEndFlow}
                          min={0}
                          precision={1}
                          onChange={val => {
                            this.$nextTick(() => {
                              const obj = this.summaries[rowInfo.itemIndex - 3]
                              if (obj.planEndFlow < obj.planStartFlow) {
                                obj.planEndFlow = obj.planStartFlow
                              }
                            })
                          }}
                        />
                      </div>
                    )
                  }
                  if (rowInfo.rowIndex === 2) {
                    return <a-input size='small' v-model={this.remarks} />
                  }
                },
              },
            }
          }),

          {
            title: '备注',
            field: 'remark',
            align: 'center',
            minWidth: 120,
            showOverflow: true,
            slots: {
              default: ({ row, rowIndex }) => {
                return <a-input size='small' v-model={this.useWaterData[rowIndex].remarks} />
              },
            },
          },
        ]
      },
      dealSchemeColumns() {
        const dateRange = [moment(this.rowInfo.planStartDate), moment(this.rowInfo.planEndDate)]

        this.schemeColumns = [
          {
            title: '所名',
            field: 'deptName',
            align: 'center',
            width: 40,
            fixed: 'left',
            headerClassName: 'scheme-span',
            slots: {
              default: () => (
                <div style='writing-mode: vertical-rl;width: 100%; display: flex; align-items: center; letter-spacing: 6px'>
                  永济灌域
                </div>
              ),
            },
          },
          {
            title: '',
            field: 'deptName',
            align: 'center',
            minWidth: 90,
            fixed: 'left',
            headerClassName: 'hidden-cell',
            showOverflow: true,
            slots: {
              default: ({ row, rowIndex }) => {
                if (rowIndex < 3) {
                  return <div class='table-cell-box'>{row.deptName}</div>
                }
                return '永济渠'
              },
            },
          },
          {
            title: '',
            field: 'deptName',
            align: 'center',
            minWidth: 90,
            fixed: 'left',
            headerClassName: 'hidden-cell',
            showOverflow: true,
          },
          // 生成日期列
          ...[...Array(dateRange[1].diff(dateRange[0], 'day') + 1)].map((item, index) => {
            const date = moment(dateRange[0]).add(index, 'day').format('YYYY-MM-DD')

            return {
              title: moment(dateRange[0]).add(index, 'day').format('MM月DD日'),
              align: 'center',
              minWidth: 140,
              field: moment(dateRange[0]).add(index, 'day').format('YYYY-MM-DD'),
              slots: {
                default: ({ row, rowIndex }) => {
                  return (
                    <div class='table-cell-box'>
                      <a-input-number
                        size='small'
                        v-model={this.schemeData[rowIndex].recordObj[date].planStartFlow}
                        min={0}
                        precision={1}
                        disabled={true}
                      />
                      &nbsp;~&nbsp;
                      <a-input-number
                        size='small'
                        v-model={this.schemeData[rowIndex].recordObj[date].planEndFlow}
                        min={0}
                        precision={1}
                        disabled={true}
                      />
                    </div>
                  )
                },
                footer: rowInfo => {
                  // 永济渠合计
                  if (rowInfo.rowIndex === 0) {
                    return rowInfo.row[rowInfo.itemIndex]
                  }

                  if (rowInfo.rowIndex === 2) {
                    return <a-input size='small' v-model={this.remarks} />
                  }
                },
              },
            }
          }),
        ]
      },

      /** 提交按钮 */
      submitForm() {
        const params = {
          waterSchemeId: this.waterSchemeId,
          remarks: this.remarks,
          summaries: this.summaries,
          reports: this.useWaterData.map(ele => {
            return {
              ...ele,
              records: Object.values(ele.recordObj).map(item => {
                if (item.planStartFlow === undefined && item.planEndFlow === undefined) {
                  return { ...item, planStartFlow: 0, planEndFlow: 0 }
                }
                if (item.planStartFlow === undefined || item.planEndFlow === undefined) {
                  if (item.planStartFlow === undefined) {
                    return { ...item, planStartFlow: item.planEndFlow }
                  }
                  if (item.planEndFlow === undefined) {
                    return { ...item, planEndFlow: item.planStartFlow }
                  }
                }

                return item
              }),
            }
          }),
        }

        this.loading = true

        editGenerationWater(params)
          .then(response => {
            this.$message.success('成功', 3)
            this.open = false
            this.$emit('ok')
            this.$emit('close')
          })
          .finally(() => (this.loading = false))
      },
    },
  }
</script>
<style lang="less" scoped>
  :deep(.table-cell-box) {
    white-space: nowrap;
  }
  :deep(.vxe-table--header-inner-wrapper) {
    height: 40px !important;
  }
  :deep(.ant-input-number-sm input) {
    padding: 0 2px;
  }
  :deep(.ant-input-number) {
    width: 60px;
  }
  :deep(.vxe-table--render-default.size--small .vxe-body--column.is--padding .vxe-cell) {
    padding: 0 2px;
    width: 100% !important;
  }
  :deep(.ant-input-number-handler-wrap) {
    display: none;
  }
</style>
