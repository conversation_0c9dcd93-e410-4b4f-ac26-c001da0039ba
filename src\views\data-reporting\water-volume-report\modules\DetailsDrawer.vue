<template>
  <!-- 增加修改 -->
  <ant-modal :visible="open" :modal-title="formTitle" :loading="modalLoading" modalWidth="1400" @cancel="cancel"
    modalHeight="720">
    <div slot="content">
      <a-row class="form-row" :gutter="32">
        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="title">基本信息</div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">年份：</label>
            <span class="common-value-text">
              {{ form?.planYear }}
            </span>
          </div>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="title">指标水量</div>
        </a-col>
        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <VxeTable :key="41" style="margin: 0 16px; height: auto" :height="456" border size="small" :autoHeight="true"
            ref="vxeTableRef" :isShowTableHeader="false" :columns="columns" :tableData="form.list" :tablePage="false"
            :rowConfig="{ isHover: false }" :showFooter="true" :footerData="footerData"
            :footer-row-style="{ background: '#F8F8F9', fontWeight: 'bold', textAlign: 'center' }"
            :scrollY="{ enabled: true, gt: 0 }" :scrollX="{ enabled: true, gt: 0 }" />
        </a-col>
      </a-row>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
    </template>
  </ant-modal>
</template>

<script lang="jsx">
import AntModal from '@/components/pt/dialog/AntModal'
import moment from 'moment'
import VxeTable from '@/components/VxeTable/index.vue'
import { decimalFilter } from '@/utils/dealNumber.js'

import { getIrrigationIndicatorWater, addIrrigationIndicatorWater, updateIrrigationIndicatorWater } from '../services'
import * as _ from 'lodash'
export default {
  name: 'FormDrawer',
  props: ['reportUnitOptions'],
  components: { AntModal, VxeTable },
  data() {
    return {
      open: false,
      modalLoading: false,
      formTitle: '',
      form: {},
      selectedChannels: [10021, 10022, 10035, 10070], // 南边渠供水所、北边渠供水所、合济渠供水所、永济渠供水所
      columns: [],
    }
  },
  computed: {
    // 合计 全年干口 - 只计算指定的四个供水所
    totalDryAmount() {
      let sum = this.form.list?.filter(item => this.selectedChannels.includes(item.depId))
        .reduce((sum, item) => sum + parseFloat(item.dryAmount || 0), 0) || 0
      return decimalFilter(sum, 2)
    },
    // 合计 全年直口 - 只计算指定的四个供水所
    totalStraightAmount() {
      let sum = this.form.list?.filter(item => this.selectedChannels.includes(item.depId))
        .reduce((sum, item) => sum + parseFloat(item.straightAmount || 0), 0) || 0
      return decimalFilter(sum, 2)
    },
    // 合计 春夏灌干口 - 只计算指定的四个供水所
    totalSsDryAmount() {
      let sum = this.form.list?.filter(item => this.selectedChannels.includes(item.depId))
        .reduce((sum, item) => sum + parseFloat(item.ssDryAmount || 0), 0) || 0
      return decimalFilter(sum, 2)
    },
    // 合计 春夏灌直口 - 只计算指定的四个供水所
    totalSsStraightAmount() {
      let sum = this.form.list?.filter(item => this.selectedChannels.includes(item.depId))
        .reduce((sum, item) => sum + parseFloat(item.ssStraightAmount || 0), 0) || 0
      return decimalFilter(sum, 2)
    },
    // 合计 秋灌干口 - 只计算指定的四个供水所
    totalAutDryAmount() {
      let sum = this.form.list?.filter(item => this.selectedChannels.includes(item.depId))
        .reduce((sum, item) => sum + parseFloat(item.autDryAmount || 0), 0) || 0
      return decimalFilter(sum, 2)
    },
    // 合计 秋灌直口 - 只计算指定的四个供水所
    totalAutStraightAmount() {
      let sum = this.form.list?.filter(item => this.selectedChannels.includes(item.depId))
        .reduce((sum, item) => sum + parseFloat(item.autStraightAmount || 0), 0) || 0
      return decimalFilter(sum, 2)
    },
    // 合计 秋浇干口 - 只计算指定的四个供水所
    totalAutPourDryAmount() {
      let sum = this.form.list?.filter(item => this.selectedChannels.includes(item.depId))
        .reduce((sum, item) => sum + parseFloat(item.autPourDryAmount || 0), 0) || 0
      return decimalFilter(sum, 2)
    },
    // 合计 秋浇直口 - 只计算指定的四个供水所
    totalAutPourStraightAmount() {
      let sum = this.form.list?.filter(item => this.selectedChannels.includes(item.depId))
        .reduce((sum, item) => sum + parseFloat(item.autPourStraightAmount || 0), 0) || 0
      return decimalFilter(sum, 2)
    },
    // 表尾数据
    footerData() {
      return [
        {
          depId: '灌域总计',
          dryAmount: this.totalDryAmount,
          straightAmount: this.totalStraightAmount,
          ssDryAmount: this.totalSsDryAmount,
          ssStraightAmount: this.totalSsStraightAmount,
          autDryAmount: this.totalAutDryAmount,
          autStraightAmount: this.totalAutStraightAmount,
          autPourDryAmount: this.totalAutPourDryAmount,
          autPourStraightAmount: this.totalAutPourStraightAmount,
        }
      ]
    },
  },
  mounted() { },
  methods: {
    cancel() {
      this.open = false
    },
    showDetails(row) {
      this.open = true
      this.loading = true
      this.formTitle = '详情'
      getIrrigationIndicatorWater({ planYear: row.planYear }).then(res => {
        this.loading = false
        this.form = {
          ...row,
          list: res.data,
        }
        // 初始化表格列配置
        this.$nextTick(() => {
          this.dealColumns()
        })
      })
    },
    dealColumns() {
      this.columns = [
        {
          title: '单位',
          field: 'depId',
          align: 'center',
          width: 120,
          slots: {
            header: () => {
              return (
                <div class='first-col'>
                  <div class='first-col-top'>水量</div>
                  <div class='first-col-bottom'>单位</div>
                </div>
              )
            },
            default: ({ row }) => {
              return this.reportUnitOptions.find(item => item.value == row.depId)?.label || row.depId
            },
          },
        },
        {
          title: '全年指标水量（万m³）',
          children: [
            {
              title: '干口',
              field: 'dryAmount',
              minWidth: 120,
              slots: {
                default: ({ row }) => {
                  return decimalFilter(row.dryAmount, 2)
                }
              }
            },
            {
              title: '直口',
              field: 'straightAmount',
              minWidth: 120,
              slots: {
                default: ({ row }) => {
                  return decimalFilter(row.straightAmount, 2)
                }
              }
            },
          ],
        },
        {
          title: '春夏灌指标水量（万m³）',
          children: [
            {
              title: '干口',
              field: 'ssDryAmount',
              minWidth: 120,
              slots: {
                default: ({ row }) => {
                  return decimalFilter(row.ssDryAmount, 2)
                }
              }
            },
            {
              title: '直口',
              field: 'ssStraightAmount',
              minWidth: 120,
              slots: {
                default: ({ row }) => {
                  return decimalFilter(row.ssStraightAmount, 2)
                }
              }
            },
          ],
        },
        {
          title: '秋灌指标水量（万m³）',
          children: [
            {
              title: '干口',
              field: 'autDryAmount',
              minWidth: 120,
              slots: {
                default: ({ row }) => {
                  return decimalFilter(row.autDryAmount, 2)
                }
              }
            },
            {
              title: '直口',
              field: 'autStraightAmount',
              minWidth: 120,
              slots: {
                default: ({ row }) => {
                  return decimalFilter(row.autStraightAmount, 2)
                }
              }
            },
          ],
        },
        {
          title: '秋浇指标水量（万m³）',
          children: [
            {
              title: '干口',
              field: 'autPourDryAmount',
              minWidth: 120,
              slots: {
                default: ({ row }) => {
                  return decimalFilter(row.autPourDryAmount, 2)
                }
              }
            },
            {
              title: '直口',
              field: 'autPourStraightAmount',
              minWidth: 120,
              slots: {
                default: ({ row }) => {
                  return decimalFilter(row.autPourStraightAmount, 2)
                }
              }
            },
          ],
        },
      ]
    },
  },
}
</script>
<style lang="less" scoped>
@import url('~@/global.less');

.title {
  color: #333;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: bold;
}

.item {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.details-img {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  img {
    width: 100%;
    height: 100%;
  }
}

::v-deep(.header-bar) {
  .title {
    text-align: center;
    width: 100%;
    position: absolute;
  }
}

::v-deep(.vxe-table--header) {
  .first-col {
    position: relative;
    height: 40px;

    &:before {
      content: '';
      background-color: #e8eaec;
      width: 260px;
      height: 1px;
      position: absolute;
      top: 52px;
      left: -20px;
      transform: rotate(26deg);
    }

    .first-col-top {
      position: absolute;
      right: 4px;
      top: -10px;
      right: 8px;
      top: -2px;
    }

    .first-col-bottom {
      position: absolute;
      left: 4px;
      bottom: -20px;
      left: 12px;
      bottom: 0px;
    }
  }
}

::v-deep(.vxe-table--footer) {

  .vxe-body--column,
  .vxe-footer--column {
    text-align: center !important;
  }

  .vxe-cell {
    text-align: center !important;
    justify-content: center !important;
  }
}
</style>
