<template>
  <ant-modal
    :visible="open"
    :modal-title="modalTitle"
    :loading="modalLoading"
    modalWidth="1300"
    @cancel="cancel"
    modalHeight="900"
    :maskClosable="false"
  >
    <div slot="content" style="height: 100%; display: flex; flex-direction: column" class="content">
      <div layout="vertical">
        <a-form-model ref="form" :model="form" :rules="rules" layout="vertical">
          <a-row class="form-row" :gutter="32">
            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <div class="title">基本信息</div>
            </a-col>
            <a-col :lg="6" :md="6" :sm="24" :span="6">
              <a-form-model-item label="调度名称" prop="schemaName">
                <a-input v-model="form.schemaName" placeholder="请输入" />
              </a-form-model-item>
            </a-col>
            <a-col :lg="6" :md="6" :sm="24" :span="6">
              <a-form-model-item label="时间维度" prop="planType">
                <a-radio-group
                  :disabled="form.planReportId !== undefined"
                  v-model="form.planType"
                  :options="radioOptions"
                  @change="handlePlanTypeChange"
                ></a-radio-group>
              </a-form-model-item>
            </a-col>
            <a-col :lg="6" :md="6" :sm="24" :span="6">
              <a-form-model-item label="方案来源" prop="waterSchemeSource">
                <a-radio-group
                  v-model="form.waterSchemeSource"
                  :options="schemeTypeOptions"
                  @change="handleSchemeTypeChange"
                ></a-radio-group>
              </a-form-model-item>
            </a-col>
            <a-col :lg="6" :md="6" :sm="24" :span="6">
              <a-form-model-item label="用水计划" prop="waterSchemeId" v-if="form.waterSchemeSource == '1'">
                <a-select
                  allowClear
                  v-model="form.waterSchemeId"
                  placeholder="请选择"
                  :options="waterPlanList"
                  @change="waterPlanChange"
                ></a-select>
              </a-form-model-item>
              <a-form-model-item label="配水方案" prop="waterSchemeId" v-if="form.waterSchemeSource == '2'">
                <a-select
                  allowClear
                  v-model="form.waterSchemeId"
                  placeholder="请选择"
                  :options="waterSchemeList"
                  @change="handleSchemeChange"
                ></a-select>
              </a-form-model-item>
            </a-col>

            <a-col :lg="6" :md="6" :sm="24" :span="6">
              <a-form-model-item label="计划时间" prop="dateRange">
                <!-- :disabled="form.planReportId !== undefined" -->
                <a-range-picker
                  style="width: 100%"
                  v-model="form.dateRange"
                  :disabled="true"
                  :allowClear="true"
                  :placeholder="['开始时间', '结束时间']"
                  :disabledDate="disabledDate"
                  @change="handleDateRangeChange"
                />
              </a-form-model-item>
            </a-col>

            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <div class="title">
                参数设置
                <div style="margin: 0 16px 5px">
                  <span style="">评价指标设置</span>
                  <a-button type="primary" @click="handleAddEvaluate" style="margin-left: 10px">
                    <a-icon type="plus" />
                    添加评价方式
                  </a-button>
                </div>
              </div>
            </a-col>

            <!-- ref="evaluationList" -->
            <div class="evaluation-list">
              <div v-for="(method, index) in evaluationMethods" :key="index" class="evaluation-method">
                <!-- <input type="checkbox" v-model="method.selected" /> -->
                <div class="evaluation-header">
                  <span style="margin-right: 26px">评价方式{{ index + 1 }}</span>
                  <span style="color: #f5222d" @click="deleteEvaluationMethod(index)"><a-icon type="delete" /></span>
                </div>

                <div class="options">
                  <a-radio-group v-model="method.selected">
                    <div class="option">
                      <a-radio value="comprehensive">综合最优</a-radio>
                    </div>
                    <div class="option">
                      <a-radio value="stability">渠道输水稳定性</a-radio>
                    </div>
                    <div class="option">
                      <a-radio value="timeliness">供水及时性</a-radio>
                    </div>
                  </a-radio-group>
                </div>
              </div>
            </div>
          </a-row>
          <!-- :autoHeight="true" :span-method="mergeRowMethod" -->
          <VxeTable
            :key="columns.length"
            style="margin: 0 10px; height: 290px"
            ref="vxeTableRef"
            :isShowTableHeader="false"
            :columns="columns"
            :tableData="form.projectRes"
            :tablePage="false"
            :rowConfig="{ isHover: false }"
            :isDrop="false"
          />
          <!-- <VxeTable
            ref="vxeTableRef"
            :isShowTableHeader="false"
            :columns="columns"
            :tableData="tableData"
            :loading="loading"
            :autoHeight="true"
            :tablePage="false"
          /> -->
        </a-form-model>
      </div>
    </div>
    <template slot="footer">
      <a-button type="primary" @click.stop="onSubmit" :loading="loading">
        {{ step === 2 ? '提交' : '生成调度方案' }}
      </a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getOptions } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import FormWaterScheme from './FormWaterScheme.vue'
  import TableWaterScheme from './TableWaterScheme.vue'
  import { addSchema, getWaterScheme, getLastProjectList, getReportList } from '../services'
  import VxeTable from '@/components/VxeTable/index.vue'
  import getMapFlatTree from '@/utils/getMapFlatTree.js'
  import moment from 'moment'
  import { dealNumber, getFixedNum } from '@/utils/dealNumber.js'
  import { project } from '@deck.gl/core'

  export default {
    name: 'AddModal',
    components: { AntModal, VxeTable },
    props: ['radioOptions', 'schemeTypeOptions'],
    data() {
      return {
        evaluationMethods: [
          {
            selected: 'comprehensive',
          },
        ],
        waterSchemeList: [], //配水方案列表
        waterPlanList: [], //用水计划列表
        loading: false,
        modalLoading: false,
        open: false,
        modalTitle: '',
        msgId: 1 || undefined,
        msgData: {},
        step: 1,

        isGenerate: false,
        rowInfo: {},
        columns: [
          // {
          //   title: '所名',
          //   field: 'deptName',
          //   align: 'center',
          //   width: 60,
          //   fixed: 'left',
          //   headerClassName: 'custmer-span',
          //   slots: {
          //     default: () => (
          //       <div style='writing-mode: vertical-rl;width: 100%; display: flex; align-items: center; letter-spacing: 6px'>
          //         永济灌区
          //       </div>
          //     ),
          //   },
          // },
          //
          {
            title: '闸门',
            field: 'projectName',
            minwidth: 120,
          },
          {
            title: '闸前水位（m）',
            field: 'beforeWlv',
            minwidth: 120,
            ellipsis: true,
            slots: {
              default: ({ row }) => {
                return (
                  <div class='table-cell-box'>
                    <a-input-number size='small' v-model={row.beforeWlv} min={0} precision={1} style='width: 100%' />
                  </div>
                )
              },
            },
          },
          {
            title: '闸后水位（m）',
            field: 'afterWlv',
            minwidth: 120,
            ellipsis: true,
            slots: {
              default: ({ row }) => {
                return (
                  <div class='table-cell-box'>
                    <a-input-number size='small' v-model={row.afterWlv} min={0} precision={1} style='width: 100%' />
                  </div>
                )
              },
            },
          },
          {
            title: '流量（m³/s）',
            field: 'flow',
            minwidth: 120,
            ellipsis: true,
            slots: {
              default: ({ row }) => {
                return (
                  <div class='table-cell-box'>
                    <a-input-number size='small' v-model={row.flow} min={0} precision={1} style='width: 100%' />
                  </div>
                )
              },
            },
          },
        ],

        summaries: [], // 汇总数据
        // 表单参数
        form: {
          schemaStartDate: undefined,
          schemaEndDate: undefined,
          planType: 1,
          waterSchemeSource: 1,
          schemaName: undefined,
          dateRange: undefined,
          waterSchemeId: undefined,
          evaluates: [],
          projectRes: [],
        },
        rules: {
          schemaName: [{ required: true, message: '方案名称不能为空', trigger: 'blur' }],
          planType: [{ required: true, message: '时间维度不能为空', trigger: 'change' }],
          waterSchemeSource: [{ required: true, message: '方案来源不能为空', trigger: 'change' }],
          waterSchemeId: [
            {
              required: true,
              validator: (rule, value, callback) => {
                if (!value) {
                  const schemeTypeName =
                    this.form.waterSchemeSource == 1
                      ? '用水计划'
                      : this.form.waterSchemeSource == 2
                        ? '配水方案'
                        : '方案'
                  callback(new Error(`${schemeTypeName}不能为空`))
                } else {
                  callback()
                }
              },
              trigger: 'change',
            },
          ],
          dateRange: [{ required: true, message: '计划时间不能为空', trigger: 'change' }],
        },
      }
    },
    created() {
      this.init()
    },
    computed: {},
    watch: {},
    methods: {
      init() {
        this.getTable()
        this.getWaterPlanList()
      },
      //切换时间维度
      handlePlanTypeChange(e) {
        //this.form.planName = `${this.deptName}${this.radioOptions.find(item => item.value === this.form.planType)?.label}计划${this.form.dateRange[0].format('YYYY-MM-DD')}`
        this.form.dateRange = []
        this.form.waterSchemeId = undefined
        this.waterSchemeList = []
        // this.dealColumns()
        this.form.planType = e.target.value
        if (this.form.waterSchemeSource == 1) {
          this.getWaterPlanList()
        } else if (this.form.waterSchemeSource == 2) {
          this.getWaterSchemeList()
        }
      },
      //切换方案来源
      handleSchemeTypeChange(e) {
        this.form.dateRange = []
        this.form.waterSchemeId = undefined
        this.waterSchemeList = []
        // this.dealColumns()
        this.form.waterSchemeSource = e.target.value
        if (this.form.waterSchemeSource == 1) {
          this.getWaterPlanList()
        } else if (this.form.waterSchemeSource == 2) {
          this.getWaterSchemeList()
        }
      },
      //配水方案切换
      handleSchemeChange(e) {
        let tmp = this.waterSchemeList?.find(el => el.waterSchemeId == e)

        this.form.dateRange = [tmp?.planStartDate, tmp?.planEndDate]
      },
      //用水计划切换
      waterPlanChange(e) {
        let tmp = this.waterPlanList?.find(el => el.planReportId == e)

        this.form.dateRange = [tmp?.planStartDate, tmp?.planEndDate]
      },
      //获取用水计划列表
      getWaterPlanList() {
        getReportList({ planType: this.form.planType ? this.form.planType : 1 }).then(res => {
          this.waterPlanList = res.data?.map(item => ({
            ...item,
            value: item.planReportId,
            label: item.planName,
          }))
        })
      },
      //获取配水方案列表 isSend: 1已下发
      getWaterSchemeList() {
        let param = {
          isSend: null,
          pageNum: 1,
          pageSize: Number.MAX_SAFE_INTEGER,
          planType: this.form.planType ? this.form.planType : 1,
        }
        getWaterScheme(param).then(res => {
          this.waterSchemeList = res.data.data?.map(item => ({
            ...item,
            value: item.waterSchemeId, //msgId,
            label: item.msgName,
          }))
        })
      },

      disabledDate(current) {
        return current && current < moment().endOf('day')
      },
      handleDateRangeChange() {
        setTimeout(() => {
          this.$nextTick(() => {
            this.dealColumns()
          })
        }, 20)
      },

      //添加评价方式
      handleAddEvaluate() {
        if (this.evaluationMethods.length >= 3) {
          this.$message.warning('评价方式最多添加3个')
          return
        }
        this.evaluationMethods.push({
          selected: 'comprehensive',
        })
      },
      //删除指标
      deleteEvaluationMethod(index) {
        // this.evaluationMethods.splice(index, 1)
        if (this.evaluationMethods.length > 1) {
          this.evaluationMethods.splice(index, 1)
        } else {
          this.$message.warning('至少需要保留一个评价方式')
        }
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      getTable() {
        getLastProjectList().then(res => {
          this.form.projectRes = res.data
          this.form.planType = 1

          // setTimeout(() => {
          //   this.$nextTick(() => {
          //     this.dealColumns()
          //   })
          // }, 200)
        })
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.open = true
        this.modalTitle = this.step === 1 ? '新增' : '新增调度方案'
        this.getTable()
      },
      //
      handleUpdate() {
        this.open = true
        this.modalTitle = this.step === 1 ? '编辑' : '编辑调度方案'
        this.getTable()
      },

      isAnyFieldEmpty(form) {
        // 遍历每个字段
        for (let key in form) {
          if (!form.hasOwnProperty(key)) continue

          const value = form[key]

          // 特殊处理不同字段类型
          if (value === null || value === undefined) {
            return true // 空值，直接返回 true
          }

          if (Array.isArray(value)) {
            // 数组必须长度大于 0
            if (value.length === 0) {
              return true
            }
          } else if (typeof value === 'string') {
            // 字符串要去除空格判断
            if (value.trim() === '') {
              return true
            }
          } else if (typeof value === 'number') {
            // 数字类型：允许 0，只要不是 NaN 就算有效
            if (isNaN(value)) {
              return true
            }
          }
          // 其他类型（如 Date）我们假设只要不是 null/undefined 就有效
        }

        // 所有字段都有值
        return false
      },
      onSubmit() {
        this.form = {
          ...this.form,
          schemaStartDate: moment(this.form.schemaStartDate).format('YYYY-MM-DD'),
          schemaEndDate: moment(this.form.schemaEndDate).format('YYYY-MM-DD'),
          evaluates: this.evaluationMethods?.map((item, index) => {
            return {
              evaluateCode: item.selected,
              schemaModel: 0,
              type: index + 1,
            }
          }),
        }
        // console.log('is save', this.form)
        if (this.isAnyFieldEmpty(this.form)) {
          // ${emptyFields.join('、')}
          this.$message.warning(`数据不能为空，请填写！`)
          return
        }

        addSchema(this.form).then(res => {
          this.$message.success('生成调度方案成功', 3)
          // 关闭当前modal
          this.open = false
          // 使用事件通信而不是直接调用父组件方法
          this.loading = false
          this.$emit('close')
          this.$emit('ok')
          // this.$emit('showDetails', mockSchemaData)
        })
      },
    },
  }
</script>

<style lang="less" scoped>
  ::v-deep .modal-content {
    height: 100%;
  }
  .content {
    //background: url('~@/assets/images/dispatch/add.png') no-repeat;
    background-size: 100% 100%;
  }
  .title {
    font-size: 16px;
    font-weight: 700;
    //margin-bottom: 12px;
  }
  // .evaluation-container {
  //   .header {
  //     display: flex;
  //     justify-content: space-between;
  //     align-items: center;
  //     padding: 10px;
  //     border-bottom: 1px solid #ccc;
  //   }

  // }

  .evaluation-list {
    display: flex;
    overflow-x: auto;
    padding: 10px;

    .evaluation-method {
      margin-right: 10px;
      width: 220px;
      min-width: 220px;
      border: 1px solid #ccc;
      border-radius: 4px;
      display: flex;
      flex-direction: column;
      .evaluation-header {
        // padding: 6px 14px;
        padding: 6px 10px 6px 14px;
        background: #f7f8fa;
        // background: red;
        // justify-content: space-between;
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
      }
      .options {
        padding: 10px;
        margin-top: 10px;

        .option {
          display: flex;
          align-items: center;
          margin-bottom: 5px;
          height: 34px;
          line-height: 34px;
          // background: red;

          // justify-content: space-between;
          input[type='checkbox'] {
            margin-right: 5px;
          }

          input[type='number'] {
            width: 50px;
            margin-left: 5px;
          }
        }
      }
    }
  }
</style>
