<template>
  <!-- 详情页面 -->
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="1800"
    @cancel="cancel"
    modalHeight="800"
    :show-ok-btn="false"
  >
    <div slot="content">
      <div class="public-water-volume-bill-report">
        <!-- 基本信息部分 -->
        <div class="basic-info-section">
          <div class="section-header" @click="toggleBasicInfoCollapse">
            <h3 class="section-title">
              <a-icon :type="basicInfoCollapsed ? 'right' : 'down'" style="margin-right: 8px;" />
              基本信息
            </h3>
          </div>

          <div v-show="!basicInfoCollapsed" class="section-content">
            <div class="form-row">
              <div class="form-item">
                <label>计划年份：</label>
                <a-date-picker
                  v-model="formData.planYear"
                  mode="year"
                  format="YYYY"
                  placeholder="请选择年份"
                  style="width: 200px"
                  disabled
                />
              </div>
              <div class="form-item">
                <label>填报单位：</label>
                <a-select
                  v-model="formData.reportUnit"
                  placeholder="请选择填报单位"
                  style="width: 200px"
                  disabled
                >
                  <a-select-option
                    v-for="option in reportUnitOptions"
                    :key="option.value"
                    :value="option.value"
                  >
                    {{ option.label }}
                  </a-select-option>
                </a-select>
              </div>
            </div>
          </div>
        </div>

        <!-- 公管渠水量水费数据部分 -->
        <div class="water-volume-section">
          <!-- <h3 class="section-title">公管渠水量水费数据</h3> -->

          <!-- 遍历渲染多个水量水费表格组件 -->
          <WaterVolumeDetailTable
            v-for="(tableConfig, index) in waterVolumeTableConfigs"
            :key="index"
            :title="tableConfig.title"
            :table-data="tableConfig.data"
            :conversion-rate="conversionRates[tableConfig.title] || 0"
            :seasons="dynamicSeasons"
          />
        </div>

      </div>
    </div>
  </ant-modal>
</template>

<script lang="jsx">
  import AntModal from '@/components/pt/dialog/AntModal'
  import WaterVolumeDetailTable from './WaterVolumeDetailTable.vue'
  import moment from 'moment'
  import { getPublicWaterVolumeBillById, getAllIrrigationRounds } from '../service'
  import { getOrgTree } from '@/api/user'
  import { getProjectPage } from '@/api/common'

  export default {
    name: 'DetailDrawer',
    components: {
      AntModal,
      WaterVolumeDetailTable
    },
    data() {
      return {
        open: false,
        modalLoading: false,
        formTitle: '详情',
        detailId: null,
        basicInfoCollapsed: false, // 基本信息折叠状态
        formData: {
          planYear: moment(),
          reportUnit: null
        },
        // 部门相关数据
        orgTree: [],
        reportUnitOptions: [],
        // 水量水费表格配置
        waterVolumeTableConfigs: [
          { title: '支3级别水费、水量', data: [] },
          { title: '支2级别水费、水量', data: [] },
          { title: '支1级别水费、水量', data: [] },
          { title: '斗3级别水费、水量', data: [] },
          { title: '斗2级别水费、水量', data: [] },
          { title: '斗1级别水费、水量', data: [] },
          { title: '农3级别水费、水量', data: [] },
          { title: '农2级别水费、水量', data: [] },
          { title: '农1级别水费、水量', data: [] },
          { title: '毛3级别水费、水量', data: [] },
          { title: '毛2级别水费、水量', data: [] },
          { title: '毛1级别水费、水量', data: [] }
        ],
        // 折算率数据
        conversionRates: {},
        // 灌溉轮次数据
        irrigationRounds: [],
        // 动态季度配置
        dynamicSeasons: [],
        // 工程信息数据
        projectOptions: []
      }
    },
    created() {
      this.getOrgTreeData()
      this.loadProjectOptions()
    },
    methods: {
      cancel() {
        this.open = false
        this.resetData()
      },

      // 切换基本信息折叠状态
      toggleBasicInfoCollapse() {
        this.basicInfoCollapsed = !this.basicInfoCollapsed
      },

      // 加载灌溉轮次数据
      async loadIrrigationRounds(fillYear) {
        try {
          const responses = await getAllIrrigationRounds(fillYear)
          this.irrigationRounds = []
          this.dynamicSeasons = []

          responses.forEach((response, index) => {
            if (response.success && response.data) {
              const roundData = {
                round: index + 1,
                ssStart: response.data.ssStart,
                ssEnd: response.data.ssEnd
              }
              this.irrigationRounds.push(roundData)

              // 生成动态季度配置
              const seasonConfig = this.generateSeasonConfig(roundData)
              if (seasonConfig) {
                this.dynamicSeasons.push(seasonConfig)
              }
            }
          })

        } catch (error) {
          console.error('获取灌溉轮次数据失败:', error)
          this.$message.error('获取灌溉轮次数据失败')
        }
      },

      // 生成季度配置
      generateSeasonConfig(roundData) {
        const startDate = moment(roundData.ssStart)
        const endDate = moment(roundData.ssEnd)

        if (!startDate.isValid() || !endDate.isValid()) {
          return null
        }

        const months = []
        let current = startDate.clone().startOf('month')

        while (current.isSameOrBefore(endDate, 'month')) {
          months.push(current.month() + 1) // moment的月份是0-11，需要+1
          current.add(1, 'month')
        }

        if (months.length === 0) {
          return null
        }

        return {
          key: `round${roundData.round}`,
          title: `${startDate.format('M')}月-${endDate.format('M')}月`,
          months: months,
          round: roundData.round
        }
      },

      async handleView(record) {
        this.open = true
        this.detailId = record.id
        this.modalLoading = true

        try {
          await this.loadDetailData(record.id)
        } catch (error) {
          console.error('加载详情数据失败:', error)
          this.$message.error('加载详情数据失败')
        } finally {
          this.modalLoading = false
        }
      },

      // 重置数据
      resetData() {
        this.formData = {
          planYear: moment(),
          reportUnit: null
        }
        // 重置所有表格数据
        this.waterVolumeTableConfigs.forEach(config => {
          config.data = []
        })
        this.conversionRates = {}
        this.detailId = null
      },

      // 加载详情数据
      async loadDetailData(id) {
        try {
          // 调用详情接口
          const response = await getPublicWaterVolumeBillById(id)
          if (response.success && response.data) {
            const data = response.data

            // 设置基本信息
            this.formData = {
              planYear: moment(data.fillYear, 'YYYY'),
              reportUnit: data.depId
            }

            // 先加载灌溉轮次数据，确保动态季度配置正确
            await this.loadIrrigationRounds(data.fillYear)

            // 确保工程信息数据已加载
            if (this.projectOptions.length === 0) {
              await this.loadProjectOptions()
            }

            // 解析详情数据并填充到表格中
            this.parseDetailDataToTables(data.detailList)

          } else {
            this.$message.error(response.message || '加载数据失败')
          }
        } catch (error) {
          console.error('加载详情数据失败:', error)
          throw error
        }
      },

      // 解析详情数据到表格
      parseDetailDataToTables(detailList) {
        if (!detailList || detailList.length === 0) {
          return
        }

        // 清空现有数据
        this.waterVolumeTableConfigs.forEach(config => {
          config.data = []
        })
        this.conversionRates = {}

        // 按渠道编号和级别分组，合并不同灌溉轮次的数据
        const channelGroups = {}

        detailList.forEach(detail => {
          const tableKey = this.getTableKeyFromChannelType(detail.channelType, detail.channelTypeSn)
          const channelKey = `${tableKey}_${detail.channelNo}`

          if (!channelGroups[channelKey]) {
            channelGroups[channelKey] = {
              tableKey: tableKey,
              channelData: {
                id: detail.id,
                projectId: this.getProjectId(detail.channelNo),
                channelName: this.getChannelName(detail.channelNo),
                channelCode: detail.channelNo,
                waterFeePrice20: detail.feeLeTwenty || 0,
                waterFeePrice50: detail.feeGeTwenty || 0
              },
              conversionRate: detail.conversionRate,
              irrigationRounds: {}
            }

            // 初始化所有季度数据为0
            this.dynamicSeasons.forEach(season => {
              channelGroups[channelKey].channelData[`${season.key}_contractWater`] = 0
              season.months.forEach(month => {
                channelGroups[channelKey].channelData[`${season.key}_month${month}`] = 0
              })
              channelGroups[channelKey].channelData[`${season.key}_diversifiedWater`] = 0
            })
          }

          // 找到对应的季度
          const season = this.dynamicSeasons.find(s => s.round === detail.irrigationRound)
          if (season) {
            // 填充包干水量
            if (detail.contractedVolume > 0) {
              channelGroups[channelKey].channelData[`${season.key}_contractWater`] = detail.contractedVolume
            }

            // 填充多元化供水
            if (detail.multipleSupply > 0) {
              channelGroups[channelKey].channelData[`${season.key}_diversifiedWater`] = detail.multipleSupply
            }

            // 填充实际使用水量
            if (detail.actualUsageDetailList && detail.actualUsageDetailList.length > 0) {
              detail.actualUsageDetailList.forEach(usage => {
                if (season.months.includes(usage.useMonth)) {
                  channelGroups[channelKey].channelData[`${season.key}_month${usage.useMonth}`] = usage.actualUsage
                }
              })
            }
          }
        })

        // 按表格分组并填充数据
        const tableGroups = {}
        Object.values(channelGroups).forEach(group => {
          if (!tableGroups[group.tableKey]) {
            tableGroups[group.tableKey] = {
              channels: [],
              conversionRate: group.conversionRate
            }
          }
          tableGroups[group.tableKey].channels.push(group.channelData)
        })

        // 将分组数据填充到对应的表格配置中
        Object.keys(tableGroups).forEach(tableKey => {
          const group = tableGroups[tableKey]
          const config = this.waterVolumeTableConfigs.find(c => c.title === tableKey)

          if (config) {
            config.data = group.channels
            this.conversionRates[tableKey] = group.conversionRate
          }
        })

      },

      // 根据渠道类型和序号获取表格标题
      getTableKeyFromChannelType(channelType, channelTypeSn) {
        const typeMap = {
          1: '支',
          2: '斗',
          3: '农',
          4: '毛'
        }

        const typeName = typeMap[channelType] || '支'
        return `${typeName}${channelTypeSn}级别水费、水量`
      },

      // 获取组织机构树数据
      async getOrgTreeData() {
        try {
          const response = await getOrgTree()
          if (response.success && response.data) {
            this.orgTree = response.data
            this.buildReportUnitOptions()
          }
        } catch (error) {
          console.error('获取组织机构树失败:', error)
        }
      },

      // 构建填报单位选项
      buildReportUnitOptions() {
        const options = []
        // 优先使用loginOrgId，其次使用deptId，最后使用默认值
        const currentUserDepId = this.$store.state.user.loginOrgId || this.$store.state.user.deptId || 10020

        // 递归查找匹配的部门
        const findMatchingDept = (nodes, targetDepId) => {
          for (const node of nodes) {
            // 确保ID比较时类型一致
            if (Number(node.deptId) === Number(targetDepId)) {
              return node
            }
            if (node.children && node.children.length > 0) {
              const found = findMatchingDept(node.children, targetDepId)
              if (found) return found
            }
          }
          return null
        }

        const matchedDept = findMatchingDept(this.orgTree, currentUserDepId)

        if (matchedDept) {
          // 添加当前部门
          options.push({
            value: matchedDept.deptId,
            label: matchedDept.deptName,
            deptId: matchedDept.deptId
          })

          // 如果有子部门，也添加子部门
          if (matchedDept.children && matchedDept.children.length > 0) {
            matchedDept.children.forEach(child => {
              options.push({
                value: child.deptId,
                label: child.deptName,
                deptId: child.deptId
              })
            })
          }
        }

        this.reportUnitOptions = options
      },

      // 加载工程信息数据
      async loadProjectOptions() {
        try {
          const params = {
            pageNum: 1,
            pageSize: 1000,
            districtCode: "0"
          }

          const response = await getProjectPage(params)
          if (response.code === 200 && response.data && response.data.data) {
            this.projectOptions = response.data.data
            console.log('加载工程信息数据成功:', this.projectOptions.length, '条')
          } else {
            console.warn('加载工程信息数据失败:', response.message)
          }
        } catch (error) {
          console.error('加载工程信息数据失败:', error)
        }
      },

      // 获取渠道名称
      getChannelName(channelNo) {
        if (this.projectOptions.length === 0) {
          console.warn('项目数据未加载，返回原始渠道编码')
          return channelNo
        }

        const project = this.projectOptions.find(option => option.projectCode === channelNo)
        return project ? project.projectName : channelNo
      },

      // 获取项目ID
      getProjectId(channelNo) {
        if (this.projectOptions.length === 0) {
          console.warn('项目数据未加载，返回渠道编码作为ID')
          return channelNo
        }

        const project = this.projectOptions.find(option => option.projectCode === channelNo)
        return project ? project.projectId : channelNo
      }
    }
  }
</script>

<style lang="less" scoped>
.public-water-volume-bill-report {
  background: #fff;

  .basic-info-section {
    margin-bottom: 30px;
    border: 1px solid #e8e8e8;
    border-radius: 6px;

    .section-header {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      background: #fafafa;
      border-bottom: 1px solid #e8e8e8;
      cursor: pointer;
      user-select: none;

      &:hover {
        background: #f0f0f0;
      }

      .section-title {
        margin: 0;
        font-size: 14px;
        font-weight: 600;
        color: #333;
        display: flex;
        align-items: center;
      }
    }

    .section-content {
      padding: 16px;

      .form-row {
        display: flex;
        gap: 30px;

        .form-item {
          display: flex;
          align-items: center;

          label {
            margin-right: 8px;
            font-weight: 500;
            color: #333;
          }
        }
      }
    }
  }

  .water-volume-section {
    margin-bottom: 30px;

    .section-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 15px;
      color: #333;
    }
  }
}
</style>
