import request from '@/utils/request'

// 详情
export function getSluice(params) {
  return request({
    url: '/prjstd/sluice/get',
    method: 'post',
    params,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

// 新增
export function addSluice(data) {
  return request({
    url: '/prjstd/sluice/add',
    method: 'post',
    data
  })
}

// 新增
export function updateSluice(data) {
  return request({
    url: '/prjstd/sluice/update',
    method: 'post',
    data
  })
}


//更新
export function updateWagaDetail(params) {
  return request({
    url: '/base/waga/update',
    method: 'post',
    params,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

//详情
export function getWagaDetail(params) {
  return request({
    url: '/base/waga/get',
    method: 'post',
    params,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

